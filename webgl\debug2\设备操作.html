<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备操作 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>设备操作</h1>
        </div>

        <div class="main-content">
            <!-- 运行状态面板 -->
            <div class="protection-panel">
                <div class="panel-title">运行状态</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>操作</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button start-button" onclick="startOperation()">启动</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="start-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button stop-button" onclick="stopOperation()">停止</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator stop" id="stop-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button reset-button" onclick="resetOperation()">复位</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reset-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 主控方式面板 -->
            <div class="protection-panel">
                <div class="panel-title">主控方式</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="fixedCompensation" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'voltageControl', 'powerFactor', 'reactiveVoltage'])">固定补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="fixed-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="dynamicCompensation" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'voltageControl', 'powerFactor', 'reactiveVoltage'])">动态补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="dynamic-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="voltageControl" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'voltageControl', 'powerFactor', 'reactiveVoltage'])">电压控制</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="voltage-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="powerFactor" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'voltageControl', 'powerFactor', 'reactiveVoltage'])">功率因数</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="power-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="reactiveVoltage" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'voltageControl', 'powerFactor', 'reactiveVoltage'])">无功电压</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reactive-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 辅控方式面板 -->
            <div class="protection-panel">
                <div class="panel-title">辅控方式</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="reactiveStart" onclick="selectAuxiliaryControl(this, ['reactiveStart', 'harmonicElimination', 'comprehensiveControl'])">无功启动</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reactive-start-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="harmonicElimination" onclick="selectAuxiliaryControl(this, ['reactiveStart', 'harmonicElimination', 'comprehensiveControl'])">谐波消除</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="harmonic-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="comprehensiveControl" onclick="selectAuxiliaryControl(this, ['reactiveStart', 'harmonicElimination', 'comprehensiveControl'])">综合控制</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="comprehensive-indicator"></div>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>



        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>


    <script>
        // 设备操作页面初始化（支持完整功能版本）
        document.addEventListener('DOMContentLoaded', function() {
            console.log('设备操作页面初始化（完整版）...');

            // 初始化设备操作管理器
            const manager = initDeviceOperationManager();

            // 初始化MQTT连接
            manager.initMQTTConnections();

            // 5秒后检查连接状态
            setTimeout(() => {
                console.log('🔍 MQTT连接状态检查:');
                Object.keys(manager.mqttConfigs).forEach(key => {
                    const client = manager.mqttClients[key];
                    const config = manager.mqttConfigs[key];
                    if (client) {
                        console.log(`${key}: 连接状态=${client.connected ? '✅已连接' : '❌未连接'}, 主题=${config.subscribeTopic}`);
                    } else {
                        console.log(`${key}: ❌客户端未创建`);
                    }
                });
            }, 5000);

            // 每30秒检查一次连接状态
            setInterval(() => {
                console.log('🔍 定期MQTT连接状态检查');
            }, 30000);

            // 定期更新时间戳
            setInterval(() => {
                const connectedCount = Object.values(manager.mqttClients).filter(client => client.connected).length;
                if (connectedCount > 0) {
                    updateDataTimestamp(new Date());
                }
            }, 1000);
        });
    </script>
</body>
</html>