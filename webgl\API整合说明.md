# /iot/alertLog/list API整合说明

## 📋 整合概述

本次整合将 `main.js` 和 `common/main.js` 文件中与 `/iot/alertLog/list` API接口相关的方法统一迁移到 `config.js` 文件中，消除代码重复，实现统一的API管理。

## 🔧 整合内容

### 1. 新增统一API函数

在 `config.js` 中新增以下函数：

#### `getRealTimeAlarmLogList(params)`
- **功能**：获取实时报警日志列表的通用函数
- **参数**：
  - `alertName`：报警名称（可选，不传则获取所有类型）
  - `pageNum`：页码，默认为1
  - `pageSize`：每页大小，默认为100
  - `beginTime`：开始时间（可选）
  - `endTime`：结束时间（可选）
- **返回值**：包含处理后数据的统一格式对象

#### `processAlarmLogData(rawData)`
- **功能**：处理报警日志原始数据
- **参数**：`rawData` - API返回的原始数据数组
- **返回值**：处理后的事件数据数组
- **处理逻辑**：
  - 过滤只保留"报警设备"和"故障设备"
  - 解析detail字段中的JSON数据获取事件名称
  - 按创建时间倒序排列
  - 统一数据格式

### 2. 重构现有函数

#### main.js 中的修改：
- **`loadRealTimeAlarmData()`**：改为调用统一API函数
- **`processRealTimeAlarmData()`**：保留兼容性，内部使用统一处理函数
- **新增 `processRealTimeAlarmDataFromAPI()`**：处理统一API返回的数据
- **删除**：`ALARM_API_CONFIG` 配置对象

#### common/main.js 中的修改：
- 与 main.js 相同的修改内容

## 📊 整合效果

### 代码重复消除
- ✅ 删除了重复的API配置对象
- ✅ 统一了数据处理逻辑
- ✅ 消除了相同的fetch请求代码

### 功能增强
- ✅ 支持更灵活的查询参数
- ✅ 统一的错误处理机制
- ✅ 一致的数据格式返回
- ✅ 更好的日志记录

### 向后兼容性
- ✅ 保留原有函数接口
- ✅ 现有调用代码无需修改
- ✅ 渐进式迁移支持

## 🔄 API调用流程

### 新的调用流程：
```
main.js/common/main.js
    ↓
loadRealTimeAlarmData()
    ↓
getRealTimeAlarmLogList() [config.js]
    ↓
makeHttpRequest() [config.js]
    ↓
processAlarmLogData() [config.js]
    ↓
processRealTimeAlarmDataFromAPI() [main.js]
    ↓
renderRealTimeAlarmList() [main.js]
```

### 数据处理流程：
```
原始API数据
    ↓
processAlarmLogData() 过滤和格式化
    ↓
统一格式的事件数据
    ↓
UI渲染
```

## 📁 文件结构

```
webgl/
├── config.js                    # 统一API管理
│   ├── getRealTimeAlarmLogList() # 新增：通用报警日志API
│   ├── processAlarmLogData()     # 新增：数据处理函数
│   ├── getAlertDeviceList()      # 原有：报警设备API
│   └── getFaultDeviceList()      # 原有：故障设备API
├── main.js                      # 主界面逻辑
│   ├── loadRealTimeAlarmData()   # 重构：使用统一API
│   ├── processRealTimeAlarmData() # 保留：兼容性函数
│   └── processRealTimeAlarmDataFromAPI() # 新增：处理统一API数据
├── common/main.js               # 通用界面逻辑（同main.js修改）
└── api-integration-test.html    # 整合测试页面
```

## 🧪 测试验证

### 测试内容：
1. **环境检查**：验证所有必需函数是否正确加载
2. **API函数对比**：对比原有函数与统一API的功能
3. **数据处理测试**：验证数据过滤和格式化逻辑
4. **完整集成测试**：端到端功能验证

### 测试方法：
打开 `api-integration-test.html` 页面，运行各项测试验证整合效果。

## 🚀 使用指南

### 推荐使用方式：
```javascript
// 获取所有类型的报警日志
const result = await getRealTimeAlarmLogList({
    pageSize: 100
});

// 获取特定类型的报警日志
const alertResult = await getRealTimeAlarmLogList({
    alertName: '报警设备',
    pageSize: 50
});

// 获取指定时间范围的报警日志
const timeRangeResult = await getRealTimeAlarmLogList({
    beginTime: '2024-01-01 00:00:00',
    endTime: '2024-01-31 23:59:59',
    pageSize: 200
});
```

### 数据格式：
```javascript
// 统一API返回格式
{
    success: true,
    message: '查询成功',
    total: 100,
    data: [
        {
            id: 135914,
            alertName: '报警设备',
            eventName: 'C相PWM板ROM参数故障',
            createTime: '2025-07-23 09:41:59',
            serialNumber: 'D19DWJ1674O77',
            deviceName: '报警设备',
            alertLevel: 1,
            status: 2,
            detail: '{"id": "HMI_30037_3", "name": "C相PWM板ROM参数故障", "value": "1"}',
            originalData: { /* 原始数据 */ }
        }
    ],
    rawData: [ /* 原始未处理数据 */ ],
    rawResponse: { /* 完整API响应 */ }
}
```

## ✅ 整合验证

### 验证要点：
- [x] 统一API函数正常工作
- [x] 数据处理逻辑正确
- [x] 原有功能保持兼容
- [x] 错误处理完善
- [x] 性能无明显下降

### 后续优化：
1. 可以逐步将其他页面也迁移到使用统一API
2. 考虑添加缓存机制提高性能
3. 增加更多的查询参数支持
4. 优化错误处理和重试机制

## 🎉 总结

本次API整合成功实现了：
- **代码重复消除**：统一管理 `/iot/alertLog/list` 接口调用
- **功能增强**：提供更灵活的查询参数和统一的数据处理
- **向后兼容**：现有代码无需修改即可使用
- **可维护性提升**：集中管理API逻辑，便于后续维护和扩展

整合后的代码更加简洁、可维护，为后续的功能扩展奠定了良好的基础。
