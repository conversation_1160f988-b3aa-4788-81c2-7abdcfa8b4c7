/**
 * Cookie认证问题修复工具
 * 专门用于诊断和修复JWT Token Cookie相关问题
 */

/**
 * Cookie修复工具类
 */
class CookieFixer {
    constructor() {
        this.tokenName = 'Admin-Token';
        this.debugMode = true;
    }

    /**
     * 记录调试信息
     * @param {string} message 
     * @param {any} data 
     */
    log(message, data = null) {
        if (this.debugMode) {
            console.log(`[CookieFixer] ${message}`, data || '');
        }
    }

    /**
     * 获取浏览器环境信息
     * @returns {Object} 环境信息
     */
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            cookieEnabled: navigator.cookieEnabled,
            url: location.href,
            hostname: location.hostname,
            protocol: location.protocol,
            port: location.port,
            isLocalhost: ['localhost', '127.0.0.1', '::1'].includes(location.hostname),
            isHttps: location.protocol === 'https:',
            isFileProtocol: location.protocol === 'file:',
            domain: location.hostname.split('.').slice(-2).join('.') // 获取主域名
        };
    }

    /**
     * 解析所有Cookie
     * @returns {Object} Cookie信息
     */
    parseCookies() {
        const cookies = {};
        const cookieString = document.cookie;
        
        if (!cookieString) {
            return { raw: '', parsed: {}, count: 0 };
        }

        cookieString.split(';').forEach(cookie => {
            const [name, ...valueParts] = cookie.trim().split('=');
            if (name) {
                cookies[name] = valueParts.join('='); // 处理值中包含=的情况
            }
        });

        return {
            raw: cookieString,
            parsed: cookies,
            count: Object.keys(cookies).length
        };
    }

    /**
     * 检测Cookie问题
     * @returns {Object} 问题诊断结果
     */
    diagnoseCookieIssues() {
        this.log('开始诊断Cookie问题...');
        
        const browserInfo = this.getBrowserInfo();
        const cookieInfo = this.parseCookies();
        const hasAdminToken = this.tokenName in cookieInfo.parsed;
        
        const issues = [];
        const recommendations = [];

        // 检查基础环境
        if (!navigator.cookieEnabled) {
            issues.push('浏览器Cookie功能被禁用');
            recommendations.push('请在浏览器设置中启用Cookie功能');
        }

        if (browserInfo.isFileProtocol) {
            issues.push('使用file://协议，Cookie功能受限');
            recommendations.push('请使用HTTP/HTTPS协议访问页面');
        }

        // 检查Token存在性
        if (!hasAdminToken) {
            issues.push('未找到Admin-Token Cookie');
            recommendations.push('请先执行登录操作');
        } else {
            const tokenValue = cookieInfo.parsed[this.tokenName];
            if (!tokenValue || tokenValue.length < 50) {
                issues.push('Admin-Token值异常（长度过短）');
                recommendations.push('Token可能已损坏，请重新登录');
            }
        }

        // 检查域名相关问题
        if (browserInfo.isLocalhost && !browserInfo.isHttps) {
            issues.push('本地HTTP环境可能存在Cookie限制');
            recommendations.push('考虑使用HTTPS或调整Cookie属性');
        }

        const diagnosis = {
            timestamp: new Date().toISOString(),
            browserInfo,
            cookieInfo,
            hasAdminToken,
            issues,
            recommendations,
            severity: issues.length === 0 ? 'none' : issues.length <= 2 ? 'low' : 'high'
        };

        this.log('诊断完成', diagnosis);
        return diagnosis;
    }

    /**
     * 尝试多种策略写入Cookie
     * @param {string} token JWT Token
     * @returns {Object} 写入结果
     */
    writeTokenWithStrategies(token) {
        this.log('开始尝试多种Cookie写入策略...', { tokenLength: token.length });
        
        const browserInfo = this.getBrowserInfo();
        const strategies = [];

        // 策略1: 标准写入（带编码）
        try {
            const cookie1 = `${this.tokenName}=${encodeURIComponent(token)}; path=/`;
            document.cookie = cookie1;
            strategies.push({ name: '标准编码写入', cookie: cookie1, success: this.verifyToken(token) });
        } catch (e) {
            strategies.push({ name: '标准编码写入', error: e.message, success: false });
        }

        // 策略2: 直接写入（不编码）
        try {
            const cookie2 = `${this.tokenName}=${token}; path=/`;
            document.cookie = cookie2;
            strategies.push({ name: '直接写入', cookie: cookie2, success: this.verifyToken(token) });
        } catch (e) {
            strategies.push({ name: '直接写入', error: e.message, success: false });
        }

        // 策略3: 带SameSite属性
        try {
            const sameSite = browserInfo.isHttps ? 'None; Secure' : 'Lax';
            const cookie3 = `${this.tokenName}=${encodeURIComponent(token)}; path=/; SameSite=${sameSite}`;
            document.cookie = cookie3;
            strategies.push({ name: 'SameSite策略', cookie: cookie3, success: this.verifyToken(token) });
        } catch (e) {
            strategies.push({ name: 'SameSite策略', error: e.message, success: false });
        }

        // 策略4: 最简写入
        try {
            const cookie4 = `${this.tokenName}=${encodeURIComponent(token)}`;
            document.cookie = cookie4;
            strategies.push({ name: '最简写入', cookie: cookie4, success: this.verifyToken(token) });
        } catch (e) {
            strategies.push({ name: '最简写入', error: e.message, success: false });
        }

        const successfulStrategies = strategies.filter(s => s.success);
        const result = {
            totalStrategies: strategies.length,
            successfulCount: successfulStrategies.length,
            strategies,
            bestStrategy: successfulStrategies[0] || null,
            finalVerification: this.verifyToken(token)
        };

        this.log('Cookie写入策略测试完成', result);
        return result;
    }

    /**
     * 验证Token是否正确写入和读取
     * @param {string} expectedToken 期望的Token值
     * @returns {boolean} 验证结果
     */
    verifyToken(expectedToken) {
        const cookieInfo = this.parseCookies();
        const actualToken = cookieInfo.parsed[this.tokenName];
        
        if (!actualToken) {
            return false;
        }

        // 尝试解码
        let decodedToken = actualToken;
        try {
            decodedToken = decodeURIComponent(actualToken);
        } catch (e) {
            // 如果解码失败，使用原值
        }

        return decodedToken === expectedToken;
    }

    /**
     * 清理所有相关Cookie
     */
    clearTokenCookies() {
        this.log('清理Token相关Cookie...');
        
        const expireDate = 'Thu, 01 Jan 1970 00:00:00 GMT';
        const clearStrategies = [
            `${this.tokenName}=; expires=${expireDate}; path=/`,
            `${this.tokenName}=; expires=${expireDate}; path=/; domain=${location.hostname}`,
            `${this.tokenName}=; expires=${expireDate}; path=/; domain=.${location.hostname}`,
        ];

        clearStrategies.forEach((strategy, index) => {
            try {
                document.cookie = strategy;
                this.log(`清理策略${index + 1}执行成功`);
            } catch (e) {
                this.log(`清理策略${index + 1}执行失败:`, e.message);
            }
        });
    }

    /**
     * 执行完整的Cookie修复流程
     * @param {string} token JWT Token
     * @returns {Object} 修复结果
     */
    async fixCookieIssues(token) {
        this.log('开始执行完整Cookie修复流程...');
        
        const startTime = Date.now();
        
        // 1. 诊断问题
        const diagnosis = this.diagnoseCookieIssues();
        
        // 2. 清理现有Cookie
        this.clearTokenCookies();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 3. 尝试写入Token
        const writeResult = this.writeTokenWithStrategies(token);
        
        // 4. 最终验证
        await new Promise(resolve => setTimeout(resolve, 100));
        const finalDiagnosis = this.diagnoseCookieIssues();
        
        const fixResult = {
            startTime: new Date(startTime).toISOString(),
            endTime: new Date().toISOString(),
            duration: Date.now() - startTime,
            initialDiagnosis: diagnosis,
            writeResult,
            finalDiagnosis,
            success: finalDiagnosis.hasAdminToken && writeResult.finalVerification,
            summary: {
                issuesFixed: diagnosis.issues.length - finalDiagnosis.issues.length,
                finalIssueCount: finalDiagnosis.issues.length,
                tokenWritten: finalDiagnosis.hasAdminToken,
                tokenVerified: writeResult.finalVerification
            }
        };

        this.log('Cookie修复流程完成', fixResult);
        return fixResult;
    }
}

// 创建全局实例
const cookieFixer = new CookieFixer();

// 导出到全局
if (typeof window !== 'undefined') {
    window.CookieFixer = CookieFixer;
    window.cookieFixer = cookieFixer;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CookieFixer, cookieFixer };
}
