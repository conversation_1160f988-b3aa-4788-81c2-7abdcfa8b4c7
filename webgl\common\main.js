/**
 * 桂林智源 SVG 数字化系统 - 主页面脚本
 * 包含Unity WebGL界面切换功能
 */

// ==================== Unity WebGL 界面切换功能 ====================

// 全局变量 - 循环队列切换机制
const viewQueue = [
    {
        id: 'unity',
        name: 'Unity 3D',
        displayName: '3D界面',
        containerClass: 'show-unity',
        iframeSrc: '',
        isUnity: true
    },
    {
        id: 'electrical-topology',
        name: '电气拓扑',
        displayName: '电气拓扑页面',
        containerClass: 'show-electrical-topology',
        iframeSrc: 'http://192.168.0.205/scada/topo/fullscreen?guid=5a4b41f3-0956-4e19-a861-58cb90a98549&type=3&date=' + new Date(),
        isUnity: false
    },
    {
        id: 'cooling-topology',
        name: '水冷拓扑',
        displayName: '水冷拓扑页面',
        containerClass: 'show-cooling-topology',
        iframeSrc: 'http://192.168.0.205/scada/topo/fullscreen?guid=5e8934c3-2d94-41de-ab87-303f61a3c7f8&type=3&date=' + new Date(),
        isUnity: false
    }
];

let currentViewIndex = 0; // 当前页面在队列中的索引
let loadedPages = new Set(); // 记录已加载的页面

/**
 * 初始化Unity WebGL界面切换功能 - 循环队列机制
 */
function initUnityViewSwitcher() {
    console.log('初始化Unity WebGL界面切换功能（循环队列机制）...');

    // 设置初始状态
    switchToView(0, false, true); // 切换到第一个页面（Unity 3D），不显示加载动画，强制更新

    // 预加载所有页面
    preloadAllPages();

    // 绑定导航按钮事件
    const leftArrow = document.querySelector('.nav-arrow.left');
    const rightArrow = document.querySelector('.nav-arrow.right');

    if (leftArrow) {
        leftArrow.addEventListener('click', () => {
            console.log('点击左箭头，切换到上一个页面');
            switchToPreviousView();
        });
    }

    if (rightArrow) {
        rightArrow.addEventListener('click', () => {
            console.log('点击右箭头，切换到下一个页面');
            switchToNextView();
        });
    }

    console.log('Unity WebGL界面切换功能初始化完成');
}

/**
 * 预加载所有页面的iframe
 */
function preloadAllPages() {
    viewQueue.forEach((view, index) => {
        if (!view.isUnity && view.iframeSrc) {
            preloadPage(index);
        }
    });
}

/**
 * 预加载指定页面
 */
function preloadPage(index) {
    if (loadedPages.has(index)) {
        return; // 已经加载过了
    }

    const view = viewQueue[index];
    if (view.isUnity || !view.iframeSrc) {
        return; // Unity页面或没有iframe源的页面不需要预加载
    }

    const iframe = document.getElementById(`iframe-${view.id}`);
    if (iframe && !iframe.src) {
        console.log(`预加载页面: ${view.displayName}`);
        iframe.src = view.iframeSrc;
        loadedPages.add(index);
    }
}

/**
 * 切换到上一个页面
 */
function switchToPreviousView() {
    const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
    switchToView(prevIndex);
}

/**
 * 切换到下一个页面
 */
function switchToNextView() {
    const nextIndex = (currentViewIndex + 1) % viewQueue.length;
    switchToView(nextIndex);
}

/**
 * 根据页面ID切换到指定页面
 */
function switchToViewById(viewId) {
    const index = viewQueue.findIndex(view => view.id === viewId);
    if (index !== -1) {
        switchToView(index);
    }
}

/**
 * 切换到指定索引的页面
 * @param {number} index - 页面索引
 * @param {boolean} showLoading - 是否显示加载动画
 * @param {boolean} forceUpdate - 是否强制更新（即使是当前页面）
 */
function switchToView(index, showLoading = true, forceUpdate = false) {
    if (!forceUpdate && index === currentViewIndex) {
        return; // 已经是当前页面，无需切换
    }

    const currentView = viewQueue[index];
    const container = document.querySelector('.main-container');

    if (!container) {
        console.error('未找到主容器元素');
        return;
    }

    // 显示加载动画
    if (showLoading) {
        showLoadingOverlay();
    }

    // 延迟执行切换，给加载动画时间显示
    const delay = showLoading ? 300 : 0;
    setTimeout(() => {
        // 移除所有视图类
        viewQueue.forEach(view => {
            container.classList.remove(view.containerClass);
        });

        // 添加当前视图类
        container.classList.add(currentView.containerClass);

        // 预加载当前页面（如果需要）
        if (!currentView.isUnity) {
            preloadPage(index);
        }

        // 更新当前索引
        currentViewIndex = index;

        // 隐藏加载动画
        if (showLoading) {
            hideLoadingOverlay();
        }

        // 更新页面标题和状态
        updatePageStatus();
    }, delay);
}

/**
 * 更新页面状态显示
 */
function updatePageStatus() {
    const currentView = viewQueue[currentViewIndex];

    // 更新页面标题（如果有相关元素）
    const titleElement = document.querySelector('.page-title');
    if (titleElement) {
        titleElement.textContent = currentView.displayName;
    }

    console.log(`当前页面：${currentView.displayName} (${currentViewIndex + 1}/${viewQueue.length})`);
}

/**
 * 显示加载覆盖层
 */
function showLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.add('show');
    }
}

/**
 * 隐藏加载覆盖层
 */
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// ==================== 兼容性函数 ====================

/**
 * 兼容性函数：切换到Unity页面
 */
function switchToUnity() {
    switchToViewById('unity');
}

/**
 * 兼容性函数：切换到拓扑页面
 */
function switchToTopology() {
    switchToViewById('electrical-topology');
}

// ==================== 原有功能代码 ====================

/**
 * 更新时间显示
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });

    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * 初始化页面功能
 */
function initializePage() {
    console.log('初始化页面功能...');

    try {
        // 初始化Unity WebGL界面切换功能
        initUnityViewSwitcher();

        // 初始化时间显示
        updateTime();
        setInterval(updateTime, 1000);

        // 初始化系统数据更新
        startSystemDataUpdate();

        // 初始化实时报警监控
        initRealTimeAlarmMonitoring();

        // 初始化报警筛选功能
        initAlarmFilters();

        // 添加按钮点击波纹效果
        addRippleEffect();

        console.log('页面功能初始化完成');
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
}

/**
 * Unity加载完成回调
 */
function onUnityLoaded() {
    console.log('Unity加载完成，初始化右侧面板图表');
    // 图表初始化已在charts.js的initCharts函数中完成
}

/**
 * 添加按钮点击波纹效果
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.btn, .parameter-item, .status-item');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 启动系统数据更新
 */
function startSystemDataUpdate() {
    console.log('启动系统数据更新（模拟数据模式）');
    
    // 使用定时器更新模拟数据
    setInterval(() => {
        updateElectricalParameters();
        updateCoolingParameters();
    }, 5000);
    
    // 立即更新一次
    updateElectricalParameters();
    updateCoolingParameters();
}

/**
 * 更新电气系统参数
 * 使用模拟数据更新电气参数
 */
function updateElectricalParameters() {
    // 使用模拟数据更新电气参数
    updateElectricalParametersWithSimulatedData();
}

/**
 * 使用模拟数据更新电气参数
 */
function updateElectricalParametersWithSimulatedData() {
    // 模拟电气系统参数数据
    const simulatedData = {
        'load-reactive-power-value': Math.floor(Math.random() * 100) + 'kVar',
        'power-factor-value': (0.8 + Math.random() * 0.2).toFixed(2),
        'grid-reactive-current-value': Math.floor(Math.random() * 50) + 'A',
        'bus-voltage-uab-value': (380 + Math.random() * 20).toFixed(1) + 'V',
        'bus-voltage-ubc-value': (380 + Math.random() * 20).toFixed(1) + 'V',
        'bus-voltage-uca-value': (380 + Math.random() * 20).toFixed(1) + 'V',
        'svg-current-ia-value': Math.floor(Math.random() * 30) + 'A',
        'svg-current-ib-value': Math.floor(Math.random() * 30) + 'A',
        'svg-current-ic-value': Math.floor(Math.random() * 30) + 'A'
    };

    // 更新界面元素
    Object.keys(simulatedData).forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = simulatedData[elementId];
            element.style.color = ''; // 重置颜色
        }
    });

    // 更新时间戳
    updateDataTimestamp(new Date());
}

/**
 * 更新水冷系统参数
 * 使用模拟数据更新水冷系统参数
 */
function updateCoolingParameters() {
    // 使用模拟数据更新水冷系统参数
    updateCoolingParametersWithSimulatedData();
}

/**
 * 使用模拟数据更新水冷系统参数
 */
function updateCoolingParametersWithSimulatedData() {
    // 模拟水冷系统状态数据
    const coolingStatusData = {
        'cooling-running-status': Math.random() > 0.5 ? '运行' : '停止',
        'cooling-remote-control': Math.random() > 0.5 ? '启用' : '禁用',
        'cooling-auto-mode': Math.random() > 0.5 ? '自动' : '手动'
    };

    // 更新水冷系统状态元素
    Object.keys(coolingStatusData).forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = coolingStatusData[elementId];
            element.style.color = ''; // 重置颜色
        }
    });

    // 更新数据时间戳
    updateDataTimestamp(new Date());
}

/**
 * 更新数据时间戳
 * @param {Date} timestamp - 时间戳
 * @param {string} statusText - 状态文本（可选）
 */
function updateDataTimestamp(timestamp, statusText = null) {
    const timestampElement = document.getElementById('data-timestamp');
    if (timestampElement && timestamp) {
        const timeString = timestamp.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        timestampElement.textContent = `数据更新时间: ${timeString}`;
    }

    if (statusText) {
        const statusElement = document.getElementById('data-status');
        if (statusElement) {
            statusElement.textContent = statusText;
        }
    }
}

/**
 * 初始化实时报警监控
 */
function initRealTimeAlarmMonitoring() {
    console.log('初始化实时报警监控...');
    // 这里可以添加报警监控的初始化逻辑
    // 由于移除了MQTT功能，这里只是占位函数
}

/**
 * 初始化报警筛选功能
 */
function initAlarmFilters() {
    console.log('初始化报警筛选功能...');
    // 这里可以添加报警筛选的初始化逻辑
    // 由于移除了MQTT功能，这里只是占位函数
}

/**
 * 发送Unity命令
 * @param {string} objectName - Unity对象名称
 * @param {string} methodName - 方法名称
 * @param {string} parameter - 参数（可选）
 */
function sendUnityCommand(objectName, methodName, parameter = '') {
    try {
        if (typeof unityInstance !== 'undefined' && unityInstance) {
            unityInstance.SendMessage(objectName, methodName, parameter);
            console.log(`Unity命令已发送: ${objectName}.${methodName}(${parameter})`);
        } else {
            console.warn('Unity实例未初始化，无法发送命令');
        }
    } catch (error) {
        console.error('发送Unity命令失败:', error);
    }
}

/**
 * 显示模块弹窗
 * @param {string} moduleId - 模块ID
 * @param {string} title - 标题
 * @param {string} iconClass - 图标类名
 * @param {string} iframeUrl - 页面URL地址
 */
function showModuleModal(moduleId, title, iconClass, iframeUrl = 'https://pic.rmb.bdstatic.com/bjh/down/bddf6d05be23936f9765bbe668e1fa41.gif') {
    // 在新标签页中打开页面
    window.open(iframeUrl, '_blank');

    console.log(`在新标签页打开${title}页面`);
}

/**
 * 关闭模块弹窗（兼容性函数）
 */
function closeModuleModal() {
    console.log('closeModuleModal: 此功能已不再使用');
    // 保留空函数以兼容可能的现有调用
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    // 如果页面已经加载完成，直接初始化
    initializePage();
}
