/**
 * 桂林智源 SVG 数字化系统 - 主页面脚本
 * 包含Unity WebGL界面切换功能
 */

// ==================== Unity WebGL 界面切换功能 ====================

// 全局变量 - 循环队列切换机制
const viewQueue = [
    {
        id: 'unity',
        name: 'Unity 3D',
        displayName: '3D界面',
        containerClass: 'show-unity',
        iframeSrc: '',
        isUnity: true
    },
    {
        id: 'electrical-topology',
        name: '电气拓扑',
        displayName: '电气拓扑页面',
        containerClass: 'show-electrical-topology',
        iframeSrc: 'http://*************/scada/topo/fullscreen?guid=5a4b41f3-0956-4e19-a861-58cb90a98549&type=3&date=' + new Date(),
        isUnity: false
    },
    {
        id: 'cooling-topology',
        name: '水冷拓扑',
        displayName: '水冷拓扑页面',
        containerClass: 'show-cooling-topology',
        iframeSrc: 'http://*************/scada/topo/fullscreen?guid=5e8934c3-2d94-41de-ab87-303f61a3c7f8&type=3&date=' + new Date(),
        isUnity: false
    }
];

let currentViewIndex = 0; // 当前页面在队列中的索引
let loadedPages = new Set(); // 记录已加载的页面

/**
 * 初始化Unity WebGL界面切换功能 - 循环队列机制
 */
function initUnityViewSwitcher() {
    console.log('初始化Unity WebGL界面切换功能（循环队列机制）...');

    // 设置初始状态
    switchToView(0, false, true); // 切换到第一个页面（Unity 3D），不显示加载动画，强制更新

    // 预加载所有页面
    preloadAllPages();

    // 设置键盘快捷键
    document.addEventListener('keydown', function (e) {
        if (e.ctrlKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            switchToPreviousView();
        } else if (e.ctrlKey && e.key === 'ArrowRight') {
            e.preventDefault();
            switchToNextView();
        } else if (e.ctrlKey && e.key === '1') {
            e.preventDefault();
            switchToViewById('unity');
        } else if (e.ctrlKey && e.key === '2') {
            e.preventDefault();
            switchToViewById('electrical-topology');
        } else if (e.ctrlKey && e.key === '3') {
            e.preventDefault();
            switchToViewById('cooling-topology');
        }
    });

    console.log('Unity WebGL界面切换功能初始化完成');
}

/**
 * 预加载所有页面
 */
function preloadAllPages() {
    viewQueue.forEach(view => {
        if (!view.isUnity && view.iframeSrc && !loadedPages.has(view.id)) {
            preloadPage(view);
        }
    });
}

/**
 * 预加载指定页面
 */
function preloadPage(view) {
    const iframeId = `${view.id}-iframe`;
    const iframe = document.getElementById(iframeId);

    if (iframe && !loadedPages.has(view.id)) {
        iframe.src = view.iframeSrc;

        iframe.onload = function () {
            loadedPages.add(view.id);
            console.log(`${view.displayName}预加载完成`);
        };

        iframe.onerror = function () {
            console.error(`${view.displayName}加载失败`);
        };
    }
}

/**
 * 切换到上一个页面
 */
function switchToPreviousView() {
    const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
    switchToView(prevIndex);
}

/**
 * 切换到下一个页面
 */
function switchToNextView() {
    const nextIndex = (currentViewIndex + 1) % viewQueue.length;
    switchToView(nextIndex);
}

/**
 * 根据页面ID切换到指定页面
 */
function switchToViewById(viewId) {
    const index = viewQueue.findIndex(view => view.id === viewId);
    if (index !== -1) {
        switchToView(index);
    }
}

/**
 * 切换到指定索引的页面
 */
function switchToView(index, showLoading = true, forceUpdate = false) {
    // 如果不是强制更新且索引相同，则跳过
    if (!forceUpdate && index === currentViewIndex) return;

    const targetView = viewQueue[index];
    if (!targetView) return;

    console.log(`切换到${targetView.displayName}`);

    if (showLoading) {
        showLoadingOverlay(`正在切换到${targetView.displayName}...`);
    }

    // 确保非Unity页面已加载
    if (!targetView.isUnity && !loadedPages.has(targetView.id)) {
        preloadPage(targetView);
    }

    const delay = showLoading ? 300 : 0;
    setTimeout(() => {
        const wrapper = document.getElementById('unityWrapper');
        if (wrapper) {
            wrapper.className = `unity-wrapper ${targetView.containerClass}`;
        }

        // 更新当前索引
        currentViewIndex = index;

        // 更新按钮状态
        updateArrowButtonStates();

        if (showLoading) {
            hideLoadingOverlay();
        }
    }, delay);
}

/**
 * 更新箭头按钮状态 - 循环队列机制
 */
function updateArrowButtonStates() {
    const prevArrow = document.getElementById('prev-arrow');
    const nextArrow = document.getElementById('next-arrow');

    if (!prevArrow || !nextArrow) return;

    const currentView = viewQueue[currentViewIndex];
    const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
    const nextIndex = (currentViewIndex + 1) % viewQueue.length;
    const prevView = viewQueue[prevIndex];
    const nextView = viewQueue[nextIndex];

    // 重置按钮状态
    prevArrow.classList.remove('current-view');
    nextArrow.classList.remove('current-view');

    // 设置当前视图指示（左箭头表示当前视图）
    // prevArrow.classList.add('current-view');

    // 更新工具提示
    // prevArrow.title = `当前：${currentView.displayName} | 上一个：${prevView.name} (Ctrl+←)`;
    // nextArrow.title = `下一个：${nextView.name} (Ctrl+→)`;
    prevArrow.title = `Ctrl+←`;
    nextArrow.title = `Ctrl+→`;

    console.log(`当前页面：${currentView.displayName} (${currentViewIndex + 1}/${viewQueue.length})`);
}

/**
 * 显示加载覆盖层
 * @param {string} text - 加载文本
 */
function showLoadingOverlay(text = '正在加载...') {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        const loadingText = overlay.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = text;
        }
        overlay.classList.add('show');
    }
}

/**
 * 隐藏加载覆盖层
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// ==================== 兼容性函数 ====================

/**
 * 兼容性函数：切换到Unity 3D界面
 */
function switchToUnity() {
    switchToViewById('unity');
}

/**
 * 兼容性函数：切换到电气拓扑页面
 */
function switchToTopology() {
    switchToViewById('electrical-topology');
}

// ==================== 原有功能代码 ====================
/**
 * 处理Unity WebGL嵌入、参数监控和故障信息显示
 */

// API配置已迁移到config.js中，使用统一的API管理

// 全局变量
let currentAlarmFilter = 'all'; // 当前筛选类型
let alarmRefreshInterval = null; // 自动刷新定时器
let lastAlarmUpdate = null; // 最后更新时间

// 配置常量
const ALARM_DISPLAY_CONFIG = {
    maxDisplayCount: 50, // 最大显示条数
    refreshInterval: 30000 // 刷新间隔（毫秒）
};

var unityInstance = null;
var unityIframe = null;

// 页面加载完成后初始化
window.addEventListener("load", function () {
    initMainPage();
    updateTime();
    setInterval(updateTime, 1000);
    // 初始化Unity WebGL界面切换功能
    initUnityViewSwitcher();
    // 初始化报警监控筛选
    initAlarmFilters();
    // 注释掉模拟数据更新，改为使用 MQTT 实时数据
    // setInterval(updateSystemData, 5000);
    // 注释掉模拟状态变化
    // setTimeout(() => {
    //   simulateStatusChang
    // }, 10000);
    // 初始化实时报警监控（使用真实API）
    initRealTimeAlarmMonitor();
    // 初始化ECharts图表
    setTimeout(() => {
        console.log('开始初始化ECharts图表...');
        initCharts();
    }, 1500);

    // 初始化弹窗事件监听
    initModalEvents();
});

/**
 * 更新时间显示
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * 初始化主页面
 */
function initMainPage() {
    unityIframe = document.getElementById('unity-iframe');

    // 监听来自Unity iframe的消息
    window.addEventListener('message', function (event) {
        // 确保消息来源安全
        if (event.source !== unityIframe.contentWindow) {
            return;
        }

        if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
        }
    });

    // 绑定控制按钮事件
    bindControlEvents();

    // 绑定窗口大小变化事件
    window.addEventListener('resize', function () {
        resizeAllCharts();
    });

    // 添加按钮点击波纹效果
    addRippleEffect();
}

/**
 * Unity加载完成后的回调
 */
function onUnityLoaded() {
    // 初始化右侧面板的ECharts图表
    console.log('Unity加载完成，初始化右侧面板图表');
    // 图表初始化已在charts.js的initCharts函数中完成
}

/**
 * 绑定控制按钮事件
 */
function bindControlEvents() {
    // 绑定图表控制按钮事件
    bindChartControlEvents();

    // 绑定重置视角按钮事件（如果存在）
    const resetViewBtn = document.getElementById("reset-view-btn");
    if (resetViewBtn) {
        resetViewBtn.addEventListener("click", function () {
            resetUnityView();
        });
    }
}



/**
 * 绑定图表控制按钮事件
 */
function bindChartControlEvents() {
    const chartBtns = document.querySelectorAll('.chart-btn');
    chartBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
        });
    });
}

/**
 * 向Unity发送命令
 * @param {string} target - 目标GameObject名称
 * @param {string} method - 要调用的方法名
 * @param {string} parameter - 可选参数
 */
function sendUnityCommand(target, method, parameter) {
    try {
        if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
                type: 'unityCommand',
                target: target,
                method: method,
                parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
        } else {
            console.warn('Unity iframe 未准备就绪');
        }
    } catch (error) {
        console.error('发送Unity命令失败:', error);
    }
}

/**
 * 添加按钮点击波纹效果
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function (e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 初始化报警监控筛选功能
 */
function initAlarmFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            // 移除其他按钮的active状态
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');
            filterAlarmList(filter);
        });
    });
}

/**
 * 筛选报警监控列表
 * @param {string} filter - 筛选类型：all, alarm, fault
 */
function filterAlarmList(filter) {
    currentAlarmFilter = filter;
    const alarmItems = document.querySelectorAll('.alarm-item');
    alarmItems.forEach(item => {
        switch (filter) {
            case 'all':
                item.style.display = 'grid';
                break;
            case 'alarm':
                // 只显示报警事件
                if (item.classList.contains('alarm')) {
                    item.style.display = 'grid';
                } else {
                    item.style.display = 'none';
                }
                break;
            case 'fault':
                // 只显示故障事件
                if (item.classList.contains('fault')) {
                    item.style.display = 'grid';
                } else {
                    item.style.display = 'none';
                }
                break;
        }
    });
}

/**
 * 显示参数详情页面
 * @param {string} paramType - 参数类型
 */
function showParameterDetails(paramType) {
    console.log('显示参数详情:', paramType);

    const paramNames = {
        'current': 'SVG总电流',
        'voltage': 'SVG总电压',
        'power': '功率因数',
        'frequency': '系统频率',
        'temperature': '设备温度',
        'efficiency': '运行效率'
    };

    const paramName = paramNames[paramType] || paramType;
    console.log(`即将显示${paramName}参数的详细趋势曲线与历史数据`);
    // alert(`即将显示${paramName}参数的详细趋势曲线与历史数据`);
}

/**
 * 显示水冷系统详情
 */
function showCoolingDetails() {
    console.log('显示水冷系统详情');
    // alert('即将显示水冷系统详情页，查看实时数据曲线');
}

/**
 * 更新系统状态显示
 * @param {string} status - 状态：ready, fault, charging, waiting, running
 */
function updateSystemStatus(status) {
    // 移除所有状态项的active类
    const statusItems = document.querySelectorAll('.status-item');
    statusItems.forEach(item => item.classList.remove('active'));

    // 为指定状态添加active类
    const targetItem = document.querySelector(`[data-status="${status}"]`);
    if (targetItem) {
        targetItem.classList.add('active');
    }
}



/**
 * 获取状态中文名称
 */
function getStatusName(status) {
    const statusNames = {
        'ready': '就绪',
        'fault': '故障',
        'charging': '充电',
        'waiting': '合高压等待',
        'running': '运行'
    };
    return statusNames[status] || status;
}



/**
 * 模拟系统数据更新
 */
function updateSystemData() {
    // 更新电气系统参数 - 使用ID选择器更精确地更新
    updateElectricalParameters();

    // 更新水冷系统参数
    updateCoolingParameters();
}

/**
 * 更新电气系统参数
 * 现在完全基于 MQTT 实时数据，不再使用模拟数据
 */
function updateElectricalParameters(mqttData = null) {
    const connectionStatus = getMQTTConnectionStatus();

    if (mqttData && mqttData.properties) {
        // 有 MQTT 实时数据，直接使用
        console.log('使用 MQTT 实时数据更新电气参数');
        updateElectricalParametersWithMQTTData(mqttData);
    } else if (connectionStatus.isConnected) {
        // MQTT 已连接但暂无数据，等待数据
        console.log('MQTT 已连接，等待数据...');
        updateWaitingForDataStatus();
    } else {
        // MQTT 未连接，显示连接状态
        console.log('MQTT 未连接，显示连接状态');
        updateMQTTDisconnectedStatus();
    }
}

/**
 * 更新等待数据状态
 */
function updateWaitingForDataStatus() {
    // 显示等待数据的状态
    const valueElements = [
        'load-reactive-power-value',
        'power-factor-value',
        'grid-reactive-current-value',
        'bus-voltage-uab-value',
        'bus-voltage-ubc-value',
        'bus-voltage-uca-value',
        'svg-current-ia-value',
        'svg-current-ib-value',
        'svg-current-ic-value'
    ];

    valueElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '等待数据...';
            element.style.color = '#ff4444'; // 使用红色警告颜色表示数据缺失
        }
    });

    // 更新时间戳
    updateDataTimestamp(null, '等待 MQTT 数据...');
}

/**
 * 更新水冷系统MQTT未连接状态
 * 当MQTT未连接时，所有水冷系统参数应显示红色字体的"等待"文本
 */
function updateCoolingMQTTDisconnectedStatus() {
    // 水冷系统关键参数元素ID列表
    const coolingElements = [
        'cooling-running-status',    // 运行状态
        'cooling-remote-control',    // 远程控制
        'cooling-auto-mode'          // 自动模式
    ];

    coolingElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '等待';
            element.style.color = '#ff4444'; // 使用红色警告颜色表示MQTT未连接
        }
    });

    console.log('水冷系统MQTT未连接，参数显示已更新为等待状态');
}

/**
 * 更新 MQTT 断开连接状态
 */
function updateMQTTDisconnectedStatus() {
    // 显示连接断开的状态
    const valueElements = [
        'load-reactive-power-value',
        'power-factor-value',
        'grid-reactive-current-value',
        'bus-voltage-uab-value',
        'bus-voltage-ubc-value',
        'bus-voltage-uca-value',
        'svg-current-ia-value',
        'svg-current-ib-value',
        'svg-current-ic-value'
    ];

    valueElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '连接断开';
            element.style.color = '#ff4444'; // 使用统一的红色警告颜色
        }
    });

    // 设置所有状态为未激活
    const statusElements = ['ready-status', 'fault-status', 'running-status', 'standby-status', 'hv-wait-status'];
    statusElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('active');
            element.classList.add('inactive');
        }
    });

    // 更新水冷系统MQTT未连接状态
    updateCoolingMQTTDisconnectedStatus();

    // 更新时间戳
    updateDataTimestamp(null, 'MQTT 连接断开');

}

/**
 * 使用 MQTT 实时数据更新电气参数
 * @param {Object} mqttData - MQTT 数据
 */
function updateElectricalParametersWithMQTTData(mqttData) {
    console.log('开始使用 MQTT 数据更新电气参数');

    if (!mqttData || !mqttData.properties) {
        console.warn('MQTT 数据格式无效或缺少 properties');
        return;
    }

    const properties = mqttData.properties;
    let updatedCount = 0;

    // 使用参数映射表进行统一处理
    Object.keys(properties).forEach(propertyId => {
        const propertyData = properties[propertyId];
        const value = propertyData.value;
        const mapping = mqttElectricalManager.parameterMapping[propertyId];

        if (mapping) {
            if (mapping.elementId) {
                // 有对应的界面元素
                if (mapping.type === 'status') {
                    updateStatusDisplay(mapping.elementId, value > 0);
                    console.log(`状态更新: ${mapping.name} = ${value > 0 ? '激活' : '未激活'}`);
                } else if (mapping.type === 'value') {
                    updateValueDisplay(mapping.elementId, value, mapping.unit);
                    console.log(`数值更新: ${mapping.name} = ${value} ${mapping.unit}`);
                }
                updatedCount++;
            } else {
                // 没有对应的界面元素，仅记录
                console.log(`参数记录: ${mapping.name} (${propertyId}) = ${value} ${propertyData.unit || ''}`);
            }
        } else {
            // 未知参数
            console.log(`未知参数: ${propertyData.name} (${propertyId}) = ${value} ${propertyData.unit || ''}`);
        }
    });

    console.log(`MQTT 数据更新完成，共更新 ${updatedCount} 个界面元素`);

    // 更新数据时间戳
    updateDataTimestamp(new Date());

    // 更新系统整体状态
    updateOverallSystemStatusFromMQTT(properties);
}

/**
 * 根据 MQTT 数据更新系统整体状态
 * @param {Object} properties - MQTT 属性数据
 */
function updateOverallSystemStatusFromMQTT(properties) {
    let hasRunning = false;
    let hasFault = false;
    let hasReady = false;
    let hasStandby = false;

    // 检查各种状态
    Object.keys(properties).forEach(propertyId => {
        const value = properties[propertyId].value;

        switch (propertyId) {
            case 'HMI_30039_5': // 运行
                if (value > 0) hasRunning = true;
                break;
            case 'HMI_30039_6': // 故障
            case 'HMI_30039_11': // 故障2
                if (value > 0) hasFault = true;
                break;
            case 'HMI_30039_4': // 就绪
                if (value > 0) hasReady = true;
                break;
            case 'HMI_30039_9': // 备用
                if (value > 0) hasStandby = true;
                break;
        }
    });

    // 确定整体状态优先级：故障 > 运行 > 就绪 > 备用 > 待机
    let overallStatus = 'standby';
    if (hasStandby) overallStatus = 'standby';
    if (hasReady) overallStatus = 'ready';
    if (hasRunning) overallStatus = 'running';
    if (hasFault) overallStatus = 'fault';

    updateOverallSystemStatus(overallStatus);
    console.log(`系统整体状态更新为: ${overallStatus}`);
}

/**
 * 使用模拟数据更新电气参数
 */
function updateElectricalParametersWithSimulatedData() {
    // 负载无功功率
    const loadReactivePowerElement = document.getElementById('load-reactive-power-value');
    if (loadReactivePowerElement) {
        const loadReactivePower = (-3.0 + Math.random() * 1.0).toFixed(2);
        loadReactivePowerElement.textContent = loadReactivePower + ' MVAr';
    }

    // 功率因数
    const powerFactorElement = document.getElementById('power-factor-value');
    if (powerFactorElement) {
        const powerFactor = (0.90 + Math.random() * 0.10).toFixed(3);
        powerFactorElement.textContent = powerFactor;
    }

    // 网侧负载无功电流
    const gridReactiveCurrentElement = document.getElementById('grid-reactive-current-value');
    if (gridReactiveCurrentElement) {
        const gridReactiveCurrent = (120.0 + Math.random() * 15.0).toFixed(1);
        gridReactiveCurrentElement.textContent = gridReactiveCurrent + ' A';
    }

    // 母线电压Uab
    const busVoltageUabElement = document.getElementById('bus-voltage-uab-value');
    if (busVoltageUabElement) {
        const busVoltageUab = (10.45 + Math.random() * 0.15).toFixed(2);
        busVoltageUabElement.textContent = busVoltageUab + ' kV';
    }

    // 母线电压Ubc
    const busVoltageUbcElement = document.getElementById('bus-voltage-ubc-value');
    if (busVoltageUbcElement) {
        const busVoltageUbc = (10.45 + Math.random() * 0.15).toFixed(2);
        busVoltageUbcElement.textContent = busVoltageUbc + ' kV';
    }

    // 母线电压Uca
    const busVoltageUcaElement = document.getElementById('bus-voltage-uca-value');
    if (busVoltageUcaElement) {
        const busVoltageUca = (10.45 + Math.random() * 0.15).toFixed(2);
        busVoltageUcaElement.textContent = busVoltageUca + ' kV';
    }

    // SVG电流Ia
    const svgCurrentIaElement = document.getElementById('svg-current-ia-value');
    if (svgCurrentIaElement) {
        const svgCurrentIa = (125.0 + Math.random() * 10.0).toFixed(1);
        svgCurrentIaElement.textContent = svgCurrentIa + ' A';
    }

    // SVG电流Ib
    const svgCurrentIbElement = document.getElementById('svg-current-ib-value');
    if (svgCurrentIbElement) {
        const svgCurrentIb = (125.0 + Math.random() * 10.0).toFixed(1);
        svgCurrentIbElement.textContent = svgCurrentIb + ' A';
    }

    // SVG电流Ic
    const svgCurrentIcElement = document.getElementById('svg-current-ic-value');
    if (svgCurrentIcElement) {
        const svgCurrentIc = (125.0 + Math.random() * 10.0).toFixed(1);
        svgCurrentIcElement.textContent = svgCurrentIc + ' A';
    }
}

/**
 * 更新水冷系统参数
 * @param {Object} coolingData - 水冷系统数据（可选，如果有则使用 MQTT 数据）
 */
function updateCoolingParameters(coolingData = null) {
    //log 调用堆栈
    console.log('updateCoolingParameters', new Error().stack);
    // 检查MQTT连接状态
    const mqttStatus = getMQTTConnectionStatus();
    const isCoolingConnected = mqttStatus.cooling && mqttStatus.cooling.isConnected;

    if (!isCoolingConnected) {
        // MQTT未连接时，显示等待状态
        console.log('水冷系统MQTT未连接，显示等待状态');
        updateCoolingMQTTDisconnectedStatus();
        return;
    }

    if (coolingData && coolingData.properties) {
        // 使用 MQTT 实时数据更新水冷系统参数
        console.log('使用 MQTT 实时数据更新水冷系统参数');
        updateCoolingParametersWithMQTTData(coolingData);
    } else {
        // 没有数据时也显示等待状态
        console.log('没有水冷系统数据，显示等待状态');
        updateCoolingMQTTDisconnectedStatus();
    }
}

/**
 * 使用 MQTT 实时数据更新水冷系统参数
 * @param {Object} coolingData - 水冷系统 MQTT 数据
 */
function updateCoolingParametersWithMQTTData(coolingData) {
    console.log('开始使用 MQTT 数据更新水冷系统参数');

    if (!coolingData || !coolingData.properties) {
        console.warn('水冷系统 MQTT 数据格式无效或缺少 properties');
        return;
    }

    const properties = coolingData.properties;
    let updatedCount = 0;

    // 使用参数映射表进行统一处理
    Object.keys(properties).forEach(propertyId => {
        const propertyData = properties[propertyId];
        const value = propertyData.value;
        const mapping = mqttCoolingManager ? mqttCoolingManager.parameterMapping[propertyId] : null;

        if (mapping) {
            if (mapping.elementId) {
                // 有对应的界面元素
                if (mapping.type === 'status') {
                    updateCoolingStatusDisplay(mapping.elementId, value, mapping.name);
                    console.log(`水冷系统状态更新: ${mapping.name} = ${value} (${value === 1 ? '激活' : value === 0 ? '未激活' : '无效'})`);
                } else if (mapping.type === 'value') {
                    updateCoolingValueDisplay(mapping.elementId, value, mapping.unit);
                    console.log(`水冷系统数值更新: ${mapping.name} = ${value} ${mapping.unit}`);
                }
                updatedCount++;
            } else {
                // 没有对应的界面元素，仅记录
                console.log(`水冷系统参数记录: ${mapping.name} (${propertyId}) = ${value} ${propertyData.unit || ''}`);
            }
        } else {
            // 未知参数
            console.log(`未知水冷系统参数: ${propertyData.name} (${propertyId}) = ${value} ${propertyData.unit || ''}`);
        }
    });

    console.log(`水冷系统 MQTT 数据更新完成，共更新 ${updatedCount} 个界面元素`);

    // 更新数据时间戳
    updateCoolingDataTimestamp(new Date());
}



/**
 * 更新水冷系统状态显示
 * @param {string} elementId - 元素ID
 * @param {number|null|undefined} value - 状态值（1=激活，0=未激活，null/undefined=无效）
 * @param {string} name - 状态名称
 */
function updateCoolingStatusDisplay(elementId, value, name) {
    const element = document.getElementById(elementId);
    if (element) {
        // 根据状态类型和值设置显示文本
        let displayText = '';

        // 检查值是否有效
        if (value === null || value === undefined) {
            displayText = '无效';
        } else {
            // 根据状态类型和值设置显示文本
            switch (name) {
                case '水冷系统自动模式':
                    displayText = value === 1 ? '自动' : '未自动';
                    break;
                case '水冷系统远程控制':
                    displayText = value === 1 ? '远程' : '未远程';
                    break;
                case '水冷运行状态':
                    displayText = value === 1 ? '运行' : '未运行';
                    break;
                default:
                    displayText = value === 1 ? '激活' : '未激活';
            }
        }

        element.textContent = displayText;
        // 重置颜色为默认（移除MQTT未连接时的红色）
        element.style.color = '';
        console.log(`水冷系统状态更新 - ${elementId}: ${displayText} (原始值: ${value})`);
    } else {
        console.log(`未找到水冷系统状态元素 ${elementId}，状态值: ${value}`);
    }
}

/**
 * 更新水冷系统数值显示
 * @param {string} elementId - 元素ID
 * @param {number} value - 数值
 * @param {string} unit - 单位
 */
function updateCoolingValueDisplay(elementId, value, unit) {
    const element = document.getElementById(elementId);
    if (element) {
        const formattedValue = typeof value === 'number' ? value.toFixed(1) : value;
        element.textContent = `${formattedValue} ${unit}`;
        console.log(`水冷系统数值更新 - ${elementId}: ${formattedValue} ${unit}`);
    } else {
        console.log(`未找到水冷系统数值元素 ${elementId}，数值: ${value} ${unit}`);
    }
}

/**
 * 更新水冷系统数据时间戳
 * @param {Date} timestamp - 时间戳
 */
function updateCoolingDataTimestamp(timestamp) {
    const timestampElements = document.querySelectorAll('.cooling-data-timestamp');
    const formattedTime = timestamp.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    timestampElements.forEach(element => {
        element.textContent = `水冷数据更新时间: ${formattedTime}`;
        element.style.color = '#4caf50'; // 正常数据为绿色
    });

    // 如果没有专门的时间戳元素，在控制台显示
    if (timestampElements.length === 0) {
        console.log(`水冷数据更新时间: ${formattedTime}`);
    }
}

/**
 * 更新水冷系统状态概览
 * @param {Object} data - 水冷系统数据
 * @param {Object} metadata - 数据元信息
 */
function updateCoolingSystemStatusOverview(data, metadata) {
    // 检查水冷系统状态（不再使用计数器）
    let hasRunning = false;
    let hasFault = false;
    let hasAutoMode = false;
    let hasRemoteControl = false;

    if (data.properties) {
        Object.keys(data.properties).forEach(propertyId => {
            const property = data.properties[propertyId];
            const value = property.value;

            switch (propertyId) {
                case 'HMI_33525_10': // 水冷运行状态
                    if (value > 0) hasRunning = true;
                    break;
                case 'HMI_33525_0': // 水冷系统自动模式
                    if (value > 0) hasAutoMode = true;
                    break;
                case 'HMI_33525_2': // 水冷系统远程控制
                    if (value > 0) hasRemoteControl = true;
                    break;
            }
        });
    }

    // 更新整体水冷系统状态（优先级：故障 > 运行 > 自动 > 远程 > 待机）
    const overallStatus = hasFault ? 'fault' :
        hasRunning ? 'running' :
            hasAutoMode ? 'auto' :
                hasRemoteControl ? 'remote' : 'standby';

    console.log(`水冷系统整体状态更新为: ${overallStatus}`);
}



// ==================== 新增：实时报警监控API集成 ====================

/**
 * 初始化实时报警监控
 */
function initRealTimeAlarmMonitor() {
    console.log('初始化实时报警监控...');

    // 立即加载一次数据
    loadRealTimeAlarmData();

    // 设置自动刷新
    alarmRefreshInterval = setInterval(() => {
        loadRealTimeAlarmData();
    }, ALARM_DISPLAY_CONFIG.refreshInterval);

    console.log(`实时报警监控初始化完成，自动刷新间隔：${ALARM_DISPLAY_CONFIG.refreshInterval / 1000}秒，最大显示：${ALARM_DISPLAY_CONFIG.maxDisplayCount}条`);
}

/**
 * 从API加载实时报警数据
 * 使用config.js中的统一API函数
 */
async function loadRealTimeAlarmData() {
    try {
        updateDataStatus('loading', '正在加载...');

        // 检查config.js中的函数是否可用
        if (typeof getRealTimeAlarmLogList !== 'function') {
            throw new Error('getRealTimeAlarmLogList函数未找到，请确保config.js正确加载');
        }

        console.log('开始调用统一API获取实时报警数据...');

        // 调用config.js中的统一API函数
        const result = await getRealTimeAlarmLogList({
            pageNum: 1,
            pageSize: 100
        });

        if (result.success) {
            // 使用统一API返回的处理后数据
            processRealTimeAlarmDataFromAPI(result);
            updateDataStatus('success', '数据加载成功');
            lastAlarmUpdate = new Date();
            updateLastUpdateTime();
            console.log(`实时报警数据加载成功，共${result.total}条记录，有效事件${result.data.length}条`);
        } else {
            throw new Error(result.message || '获取数据失败');
        }

    } catch (error) {
        console.error('加载实时报警数据失败:', error);
        let errorMessage = error.message;
        if (error.message.includes('CORS') || error.message.includes('fetch')) {
            errorMessage = 'CORS跨域请求被阻止，请联系管理员配置服务器允许跨域访问';
        }
        updateDataStatus('error', '加载失败: ' + errorMessage);
        showAlarmError(errorMessage);
    }
}

/**
 * 处理实时报警API数据（原始版本，保留兼容性）
 * @param {Object} data - API返回的原始数据
 */
function processRealTimeAlarmData(data) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) {
        console.error('找不到alarm-list元素');
        return;
    }

    // 清空现有数据
    alarmList.innerHTML = '';

    if (!data.rows || data.rows.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无报警事件</p>
            </div>
          `;
        return;
    }

    console.log('处理报警数据，总数:', data.rows.length);

    // 使用config.js中的统一数据处理函数
    if (typeof processAlarmLogData === 'function') {
        const processedEvents = processAlarmLogData(data.rows);

        // 限制显示数量
        const displayEvents = processedEvents.slice(0, ALARM_DISPLAY_CONFIG.maxDisplayCount);

        // 渲染事件列表
        renderRealTimeAlarmList(displayEvents);
    } else {
        console.error('processAlarmLogData函数未找到，请确保config.js正确加载');
    }
}

/**
 * 处理来自统一API的实时报警数据
 * @param {Object} result - 统一API返回的结果对象
 */
function processRealTimeAlarmDataFromAPI(result) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) {
        console.error('找不到alarm-list元素');
        return;
    }

    // 清空现有数据
    alarmList.innerHTML = '';

    if (!result.data || result.data.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无报警事件</p>
            </div>
          `;
        return;
    }

    console.log('处理统一API报警数据，总数:', result.total, '有效事件:', result.data.length);

    // 限制显示数量
    const displayEvents = result.data.slice(0, ALARM_DISPLAY_CONFIG.maxDisplayCount);

    // 渲染事件列表
    renderRealTimeAlarmList(displayEvents);
}

/**
 * 渲染实时报警列表
 * @param {Array} events - 事件数组
 */
function renderRealTimeAlarmList(events) {
    const alarmList = document.querySelector('.alarm-list');
    if (!alarmList) return;

    if (events.length === 0) {
        alarmList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>暂无符合条件的报警事件</p>
            </div>
          `;
        return;
    }

    const eventsHtml = events.map((event, index) => {
        // 确定事件类型的CSS类名
        const eventTypeClass = event.alertName === '报警设备' ? 'alarm' : 'fault';

        // 格式化时间显示（合并日期和时间为一列）
        const formattedTime = formatAlarmDisplayTime(event.createTime);

        // 倒序序号：最新的事件显示为序号1
        const serialNumber = events.length - index;

        return `
            <div class="alarm-item ${eventTypeClass}">
              <span class="alarm-serial">${serialNumber}</span>
              <span class="alarm-datetime">${formattedTime}</span>
              <span class="alarm-message" title="${event.eventName}">${event.eventName}</span>
            </div>
          `;
    }).join('');

    alarmList.innerHTML = eventsHtml;

    // 应用当前筛选条件
    filterAlarmList(currentAlarmFilter);
}

/**
 * 格式化报警时间显示
 * @param {string} timeString - 时间字符串
 * @returns {string} 格式化后的时间字符串
 */
function formatAlarmDisplayTime(timeString) {
    if (!timeString) return '';

    // 如果已经是正确格式，直接返回
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeString)) {
        return timeString;
    }

    // 尝试解析并格式化
    try {
        const date = new Date(timeString);
        if (isNaN(date.getTime())) {
            return timeString; // 如果解析失败，返回原字符串
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        console.warn('时间格式化失败:', error, timeString);
        return timeString;
    }
}

/**
 * 更新数据状态显示
 * @param {string} status - 状态：loading, success, error
 * @param {string} message - 状态消息
 */
function updateDataStatus(status, message) {
    const dataStatus = document.getElementById('dataStatus');
    if (dataStatus) {
        dataStatus.textContent = message;
        dataStatus.className = `data-status ${status}`;
    }
}

/**
 * 更新最后更新时间显示
 */
function updateLastUpdateTime() {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement && lastAlarmUpdate) {
        const timeStr = lastAlarmUpdate.toLocaleTimeString();
        lastUpdateElement.textContent = `最后更新: ${timeStr}`;
    }
}

/**
 * 显示报警错误信息
 * @param {string} message - 错误信息
 */
function showAlarmError(message) {
    const alarmList = document.querySelector('.alarm-list');
    if (alarmList) {
        alarmList.innerHTML = `
            <div class="empty-state error">
              <i class="fas fa-exclamation-triangle"></i>
              <p>${message}</p>
              <button class="retry-btn" onclick="loadRealTimeAlarmData()">
                <i class="fas fa-redo"></i>
                重新加载
              </button>
            </div>
          `;
    }
}

/**
 * 切换主菜单显示/隐藏
 */
function toggleMainMenu() {
    const dropdown = document.getElementById('mainMenuDropdown');
    dropdown.classList.toggle('show');

    // 点击其他地方关闭菜单
    document.addEventListener('click', function (event) {
        if (!event.target.closest('.header-center')) {
            dropdown.classList.remove('show');
        }
    });
}

/**
 * 导航到指定模块
 * @param {string} module - 模块名称
 */
function navigateToModule(module) {
    const dropdown = document.getElementById('mainMenuDropdown');
    dropdown.classList.remove('show');

    // 这里可以根据需要实现具体的导航逻辑
    console.log(`导航到模块: ${module}`);

    // 根据模块类型显示对应的弹窗
    switch (module) {
        case 'realtime-curve':
            showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line', '实时数据.html');
            break;
        case 'history-record':
            showModuleModal('history-record', '历史记录', 'fas fa-history', '历史记录.html');
            break;
        case 'history-event':
            showModuleModal('history-event', '历史事件', 'fas fa-calendar-alt', '历史事件.html');
            break;
        case 'fault-wave':
            showModuleModal('fault-wave', '故障录波', 'fas fa-wave-square', '故障录波.html');
            break;
        case 'parameter-curve':
            showModuleModal('parameter-curve', '参数曲线', 'fas fa-chart-area', '参数曲线.html');
            break;
        case 'dsp':
            showModuleModal('dsp', 'DSP', 'fas fa-microchip', 'http://*************/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date=' + new Date());
            break;
        case 'version-info':
            showModuleModal('version-info', '版本信息', 'fas fa-info-circle', 'http://*************/scada/topo/fullscreen?guid=f166d35a-180f-4fbf-91ab-a63229da3391&type=3&date=' + new Date());
            break;
        default:
            console.warn(`未知模块: ${module}`);
    }
}

/**
 * 获取模块中文名称
 * @param {string} module - 模块英文名称
 * @returns {string} 中文名称
 */
function getModuleName(module) {
    const moduleNames = {
        'realtime-curve': '实时曲线',
        'history-record': '历史记录',
        'history-event': '历史事件',
        'fault-wave': '故障录波',
        'parameter-curve': '参数曲线',
        'version-info': '版本信息'
    };
    return moduleNames[module] || module;
}

/**
 * 打开水冷系统拓扑图
 */
function openCoolingTopology() {
    console.log('打开水冷系统拓扑图');
    // showTopologyModal('cooling');
    let date = new Date();
    showModuleModal('cooling-topology', '水冷系统拓扑图', 'fas fa-tint', 'http://*************/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date=' + date);
}

/**
 * 打开SVG系统拓扑图
 */
function openSystemTopology() {
    console.log('打开SVG系统拓扑图');
    // showTopologyModal('electrical');
    let date = new Date();
    showModuleModal('electrical-topology', '电气系统拓扑图', 'fas fa-bolt', 'http://*************/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date=' + date);
}

/**
 * 打开I/O状态页面
 */
function openIOStatus() {
    console.log('打开I/O状态页面');
    // showIOStatusModal();
    let date = new Date();
    showModuleModal('io-status', 'I/O状态监控', 'fas fa-plug', 'http://*************/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date=' + date);
}

/**
 * 打开单元状态页面
 */
function openUnitStatus() {
    console.log('打开单元状态页面');
    // showUnitStatusModal();
    let date = new Date();
    showModuleModal('unit-status', '单元状态监控', 'fas fa-microchip', 'http://*************/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&wework_cfm_code=NOhs%2BuVWHo97Du860XjXIPC5tyE8DwbeJo3xoLtc8tn94QGaXR9LJW5VvFSCVJID5Fpwj6f%2FVjFU6LVqrXItFhckh8qcSUUqInRO3%2Fb3FD0Ee6bfED2vOqLVG6i2ymNIFQ7%2Fi%2BDxy8I7Xi5S1uPRzyxBjWWrv5w9p218BH1F2vIV&date=' + date);
}

/**
 * 打开主控|辅控页面
 */
function openMasterControl() {
    console.log('打开主控|辅控页面');
    let date = new Date();
    showModuleModal('master-control', '主控|辅控', 'fas fa-sitemap', 'http://*************/scada/topo/fullscreen?guid=6f1379ce-d7b5-4017-9d9d-78d49813cd8c&type=3&date=' + date);
}

/**
 * 打开调试参数1页面
 */
function openDebugParams1() {
    console.log('打开调试参数1页面');
    showModuleModal('debug-params-1', '调试参数1', 'fas fa-cogs', '调试参数1.html');
}

/**
 * 打开调试参数2页面
 */
function openDebugParams2() {
    console.log('打开调试参数2页面');
    showModuleModal('debug-params-2', '调试参数2', 'fas fa-tools', '调试参数2.html');
}

/**
 * 重置Unity视角
 */
function resetUnityView() {
    sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
    console.log('重置Unity视角');
}

/**
 * 显示I/O状态弹窗
 */
function showIOStatusModal() {
    const modal = document.getElementById('ioStatusModal');
    if (modal) {
        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        // 模拟I/O状态更新
        simulateIOStatusUpdate();
    }
}

/**
 * 关闭I/O状态弹窗
 */
function closeIOStatusModal() {
    const modal = document.getElementById('ioStatusModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

/**
 * 模拟I/O状态更新
 */
function simulateIOStatusUpdate() {
    // 随机更新一些I/O状态
    const indicators = document.querySelectorAll('#ioStatusModal .io-indicator');

    // 定期更新状态
    const updateInterval = setInterval(() => {
        // 随机选择几个指示器进行状态切换
        const randomIndicators = Array.from(indicators)
            .sort(() => 0.5 - Math.random())
            .slice(0, Math.floor(Math.random() * 5) + 1);

        randomIndicators.forEach(indicator => {
            if (Math.random() > 0.7) { // 30%的概率切换状态
                if (indicator.classList.contains('active')) {
                    indicator.classList.remove('active');
                    indicator.classList.add('inactive');
                } else {
                    indicator.classList.remove('inactive');
                    indicator.classList.add('active');
                }
            }
        });
    }, 3000);

    // 当弹窗关闭时停止更新
    const modal = document.getElementById('ioStatusModal');
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                if (modal.style.display === 'none') {
                    clearInterval(updateInterval);
                    observer.disconnect();
                }
            }
        });
    });
    observer.observe(modal, { attributes: true });
}

/**
 * 显示拓扑图弹窗
 * @param {string} type - 拓扑图类型：'electrical' 或 'cooling'
 */
function showTopologyModal(type) {
    const modal = document.getElementById('topologyModal');
    const title = document.getElementById('topologyModalTitle');
    const image = document.getElementById('topologyImage');
    const imageContainer = document.querySelector('.topology-image-container');
    const iframeContainer = document.getElementById('topologyIframeContainer');
    const iframe = document.getElementById('topologyIframe');

    if (modal && title && image && imageContainer && iframeContainer && iframe) {
        // 设置标题和内容
        if (type === 'electrical') {
            title.innerHTML = '<i class="fas fa-bolt"></i>电气系统拓扑图';
            image.src = './image/SVG系统拓扑图.png';
            image.alt = '电气系统拓扑图';
            // 显示图片，隐藏iframe
            imageContainer.style.display = 'block';
            iframeContainer.style.display = 'none';
        } else if (type === 'cooling') {
            title.innerHTML = '<i class="fas fa-tint"></i>水冷系统拓扑图';
            // 设置iframe URL
            iframe.src = 'http://*************/scada/topo/fullscreen?guid=29bd5260-38c5-4b4f-97a0-66e97903f9e9';
            // 隐藏图片，显示iframe
            imageContainer.style.display = 'none';
            iframeContainer.style.display = 'block';
        }

        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

/**
 * 关闭拓扑图弹窗
 */
function closeTopologyModal() {
    const modal = document.getElementById('topologyModal');
    const iframe = document.getElementById('topologyIframe');

    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            // 清理iframe内容以释放资源
            if (iframe) {
                iframe.src = '';
            }
        }, 300);
    }
}

/**
 * 显示单元状态弹窗
 */
function showUnitStatusModal() {
    const modal = document.getElementById('unitStatusModal');
    if (modal) {
        modal.style.display = 'flex';
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        // 初始化单元状态数据
        initUnitStatusData();
    }
}

/**
 * 关闭单元状态弹窗
 */
function closeUnitStatusModal() {
    const modal = document.getElementById('unitStatusModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

/**
 * 显示模块弹窗（修改为在新标签页打开）
 * @param {string} moduleId - 模块ID
 * @param {string} title - 弹窗标题
 * @param {string} iconClass - 图标类名
 * @param {string} iframeUrl - 页面URL地址
 */
function showModuleModal(moduleId, title, iconClass, iframeUrl = 'https://pic.rmb.bdstatic.com/bjh/down/bddf6d05be23936f9765bbe668e1fa41.gif') {
    // 在新标签页中打开页面
    window.open(iframeUrl, '_blank');

    console.log(`在新标签页打开${title}页面`);
}

/**
 * 关闭模块弹窗（已不再使用，保留函数以兼容现有代码）
 */
function closeModuleModal() {
    // 由于模块内容现在在新标签页中打开，此函数不再需要执行任何操作
    console.log('closeModuleModal: 此功能已不再使用');
    // 保留空函数以兼容可能的现有调用
}

/**
 * 关闭单元详细信息
 */
function closeUnitDetail() {
    // 取消选中状态
    const selectedUnit = document.querySelector('.unit-item.selected');
    if (selectedUnit) {
        selectedUnit.classList.remove('selected');
    }
    // 隐藏详细信息面板（可以添加动画效果）
    const detailSection = document.querySelector('.unit-detail-section');
    if (detailSection) {
        detailSection.style.opacity = '0.5';
        setTimeout(() => {
            detailSection.style.opacity = '1';
        }, 200);
    }
}

/**
 * 初始化单元状态数据和交互
 */
function initUnitStatusData() {
    // 为单元项添加点击事件
    const unitItems = document.querySelectorAll('.unit-item');
    unitItems.forEach(item => {
        item.addEventListener('click', function () {
            // 移除其他选中状态
            unitItems.forEach(u => u.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');

            // 更新详细信息
            const unitId = this.dataset.unit;
            updateUnitDetail(unitId);
        });
    });

    // 模拟实时数据更新
    simulateUnitStatusUpdate();
}

/**
 * 更新单元详细信息
 * @param {string} unitId - 单元ID
 */
function updateUnitDetail(unitId) {
    const titleElement = document.getElementById('selectedUnitTitle');
    if (titleElement) {
        titleElement.textContent = `${unitId} 单元详细状态`;
    }

    // 模拟不同单元的状态数据
    const statusData = getUnitStatusData(unitId);
    const statusItems = document.querySelectorAll('.unit-detail-section .status-item');

    statusItems.forEach((item, index) => {
        const indicator = item.querySelector('.status-indicator');
        if (indicator && statusData[index] !== undefined) {
            if (statusData[index]) {
                indicator.classList.remove('inactive');
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
                indicator.classList.add('inactive');
            }
        }
    });
}

/**
 * 获取单元状态数据（模拟数据）
 * @param {string} unitId - 单元ID
 * @returns {Array} 状态数组
 */
function getUnitStatusData(unitId) {
    // 模拟不同单元的状态数据
    const statusPatterns = {
        'A01': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
        'A02': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
        'A03': [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false],
        'A12': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false]
    };

    return statusPatterns[unitId] || statusPatterns['A12'];
}

/**
 * 模拟单元状态实时更新
 */
function simulateUnitStatusUpdate() {
    const updateInterval = setInterval(() => {
        // 随机更新单元电压值
        const unitItems = document.querySelectorAll('.unit-item');
        unitItems.forEach(item => {
            const voltageSpan = item.querySelector('.unit-voltage');
            if (voltageSpan && Math.random() > 0.8) { // 20%概率更新
                const baseVoltage = parseInt(voltageSpan.textContent);
                const variation = Math.floor(Math.random() * 6) - 3; // -3到+3的变化
                const newVoltage = Math.max(735, Math.min(745, baseVoltage + variation));
                voltageSpan.textContent = newVoltage.toString();
            }
        });

        // 随机更新指示器状态
        const indicators = document.querySelectorAll('.unit-item .indicator-dot');
        indicators.forEach(indicator => {
            if (Math.random() > 0.9) { // 10%概率切换状态
                if (indicator.classList.contains('active')) {
                    indicator.classList.remove('active');
                    indicator.classList.add('inactive');
                } else {
                    indicator.classList.remove('inactive');
                    indicator.classList.add('active');
                }
            }
        });

        // 更新电压统计显示
        updateVoltageStats();
    }, 2000);

    // 当弹窗关闭时停止更新
    const modal = document.getElementById('unitStatusModal');
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                if (modal.style.display === 'none') {
                    clearInterval(updateInterval);
                    observer.disconnect();
                }
            }
        });
    });
    observer.observe(modal, { attributes: true });
}

/**
 * 更新电压统计显示
 */
function updateVoltageStats() {
    const voltageValues = document.querySelectorAll('.voltage-value');
    voltageValues.forEach(valueElement => {
        if (Math.random() > 0.7) { // 30%概率更新
            const currentValue = parseInt(valueElement.textContent);
            const variation = Math.floor(Math.random() * 4) - 2; // -2到+2的变化
            const newValue = Math.max(735, Math.min(745, currentValue + variation));
            valueElement.textContent = `${newValue} V`;
        }
    });
}

// ==================== MQTT 电气系统实时数据更新功能 ====================

/**
 * MQTT 电气系统数据管理器
 * 负责 MQTT 连接、数据订阅、数据处理和界面更新
 */
class MQTTElectricalDataManager {
    constructor() {
        this.mqttClient = null;
        this.isConnected = false;
        this.subscriptionTopic = '/189/D19QBHKRZ791U/ws/service';
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5秒重连间隔
        this.dataUpdateCallbacks = [];
        this.lastDataUpdate = null;

        // 初始化数据处理器
        this.dataProcessor = new ElectricalDataProcessor();

        // 电气系统参数映射表（基于实际界面元素和 MQTT 数据）
        this.parameterMapping = {
            // 状态参数映射（映射到实际存在的界面元素）
            'HMI_30039_4': { name: '就绪', elementId: 'ready-status', type: 'status' },
            'HMI_30039_5': { name: '运行', elementId: 'running-status', type: 'status' },
            'HMI_30039_6': { name: '故障', elementId: 'fault-status', type: 'status' },
            'HMI_30039_9': { name: '备用', elementId: 'standby-status', type: 'status' },
            'HMI_30039_10': { name: '合高压等待', elementId: 'hv-wait-status', type: 'status' },

            // 电压参数映射（映射到实际存在的界面元素）
            'HMI_32030': { name: '母线电压Uab', elementId: 'bus-voltage-uab-value', type: 'value', unit: 'kV' },
            'HMI_32032': { name: '母线电压Ubc', elementId: 'bus-voltage-ubc-value', type: 'value', unit: 'kV' },
            'HMI_32034': { name: '母线电压Uca', elementId: 'bus-voltage-uca-value', type: 'value', unit: 'kV' },

            // 电流参数映射（映射到实际存在的界面元素）
            'HMI_32040': { name: 'SVG电流Ia', elementId: 'svg-current-ia-value', type: 'value', unit: 'A' },
            'HMI_32042': { name: 'SVG电流Ib', elementId: 'svg-current-ib-value', type: 'value', unit: 'A' },
            'HMI_32044': { name: 'SVG电流Ic', elementId: 'svg-current-ic-value', type: 'value', unit: 'A' },
            'HMI_32046': { name: '网侧负载无功电流', elementId: 'grid-reactive-current-value', type: 'value', unit: 'A' },

            // 功率参数映射（映射到实际存在的界面元素）
            'HMI_32048': { name: '负载无功功率', elementId: 'load-reactive-power-value', type: 'value', unit: 'MVAr' },
            'HMI_32050': { name: '功率因数', elementId: 'power-factor-value', type: 'value', unit: '' },

            // 其他状态参数（记录但可能没有对应的界面元素）
            'HMI_30039_0': { name: '起始状态', elementId: null, type: 'status' },
            'HMI_30039_1': { name: '充电', elementId: null, type: 'status' },
            'HMI_30039_2': { name: '单元自检', elementId: null, type: 'status' },
            'HMI_30039_3': { name: '复位', elementId: null, type: 'status' },
            'HMI_30039_7': { name: '高压', elementId: null, type: 'status' },
            'HMI_30039_8': { name: '开机自检', elementId: null, type: 'status' },
            'HMI_30039_11': { name: '故障2', elementId: null, type: 'status' },
            'HMI_30039_12': { name: '复位2', elementId: null, type: 'status' }
        };

        this.init();
    }

    /**
     * 初始化 MQTT 电气系统数据管理器
     */
    async init() {
        console.log('初始化 MQTT 电气系统数据管理器...');
        try {
            await this.connectMQTT();
            await this.subscribeToTopic();
            this.setupMessageHandler();
            this.setupReconnectHandler();
            console.log('MQTT 电气系统数据管理器初始化完成');
        } catch (error) {
            console.error('MQTT 电气系统数据管理器初始化失败:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * 连接 MQTT 服务器
     */
    async connectMQTT() {
        return new Promise((resolve, reject) => {
            try {
                // 使用已验证的 MQTT 配置
                const options = {
                    username: 'FastBee',
                    password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.1O79OeRWpHjRa5nps9L9ETtplMTLmUiiZpmLi-waRkGnM4M72fgWdj4QGxqx_-ccCedyixw5jpfeWtniY5RHXQ',
                    cleanSession: true,
                    keepAlive: 30,
                    clientId: 'web-' + Math.random().toString(16).substr(2),
                    connectTimeout: 60000,
                };

                // 使用已验证的 MQTT 服务器地址
                const url = 'ws://*************:8083/mqtt';
                console.log('连接到 MQTT 服务器：', url);

                // 使用全局 mqtt 对象连接
                if (typeof mqtt !== 'undefined') {
                    this.mqttClient = mqtt.connect(url, options);
                } else {
                    throw new Error('MQTT 客户端库未加载');
                }

                this.mqttClient.on('connect', () => {
                    console.log('MQTT 电气系统连接成功');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;

                    // 记录连接事件
                    logConnectionEvent('connected', '成功连接到 MQTT 服务器');

                    resolve();
                });

                this.mqttClient.on('error', (error) => {
                    console.error('MQTT 电气系统连接失败:', error);
                    this.isConnected = false;

                    // 记录错误事件
                    logErrorEvent(error.message, 'MQTT 连接失败');

                    reject(error);
                });

                this.mqttClient.on('close', () => {
                    console.log('MQTT 电气系统连接已断开');
                    this.isConnected = false;

                    // 记录连接事件
                    logConnectionEvent('disconnected', '连接已断开');
                });

            } catch (error) {
                console.error('MQTT 连接配置错误:', error);
                reject(error);
            }
        });
    }

    /**
     * 订阅电气系统数据主题
     */
    async subscribeToTopic() {
        return new Promise((resolve, reject) => {
            if (!this.mqttClient || !this.isConnected) {
                reject(new Error('MQTT 客户端未连接'));
                return;
            }

            this.mqttClient.subscribe(this.subscriptionTopic, { qos: 1 }, (err, granted) => {
                if (err) {
                    console.error('订阅电气系统主题失败:', err);
                    reject(err);
                } else {
                    console.log('成功订阅电气系统主题:', this.subscriptionTopic);
                    console.log('订阅详情:', granted);
                    resolve(granted);
                }
            });
        });
    }

    /**
     * 设置消息处理器
     */
    setupMessageHandler() {
        if (!this.mqttClient) return;

        this.mqttClient.on('message', (topic, message) => {
            try {
                if (topic === this.subscriptionTopic) {
                    const data = JSON.parse(message.toString());
                    console.log('收到电气系统数据:', data);
                    this.processElectricalData(data);
                }
            } catch (error) {
                console.error('处理 MQTT 消息失败:', error);
                console.error('原始消息:', message.toString());
            }
        });
    }

    /**
     * 设置重连处理器
     */
    setupReconnectHandler() {
        if (!this.mqttClient) return;

        this.mqttClient.on('reconnect', () => {
            console.log('正在重连 MQTT 电气系统...');
            this.reconnectAttempts++;
        });

        this.mqttClient.on('offline', () => {
            console.log('MQTT 电气系统离线');
            this.isConnected = false;
            this.scheduleReconnect();
        });
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error(`MQTT 重连次数超过限制 (${this.maxReconnectAttempts})，停止自动重连`);
            console.log('您可以手动调用 reconnectMQTT() 函数进行重连');

            // 更新界面状态为连接失败
            updateMQTTDisconnectedStatus();
            return;
        }

        // 使用指数退避算法，避免频繁重连
        const backoffDelay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts), 60000);

        console.log(`第 ${this.reconnectAttempts + 1} 次尝试重连 MQTT 电气系统，${backoffDelay / 1000}秒后重试...`);

        setTimeout(() => {
            this.init();
        }, backoffDelay);
    }

    /**
     * 处理电气系统数据
     * @param {Object} data - 接收到的数据
     */
    processElectricalData(data) {
        try {
            console.log('开始处理电气系统数据');

            // 基本数据验证
            if (!this.validateIncomingData(data)) {
                console.error('接收到的数据格式无效，跳过处理');
                return;
            }

            // 使用数据处理器处理数据
            const processingResult = this.dataProcessor.processData(data);

            if (!processingResult.success) {
                console.error('数据处理失败:', processingResult.error);
                if (processingResult.details) {
                    console.error('详细信息:', processingResult.details);
                }

                // 处理失败时不更新界面，但记录错误
                this.handleDataProcessingError(processingResult.error);
                return;
            }

            const processedData = processingResult.data;
            const metadata = processingResult.metadata;

            // 更新最后数据时间
            this.lastDataUpdate = new Date();

            // 记录数据质量信息
            console.log(`数据质量评估: ${metadata.dataQuality.score.toFixed(1)}% (${metadata.dataQuality.level})`);

            if (metadata.integrityCheck.warnings.length > 0) {
                console.warn('数据完整性警告:', metadata.integrityCheck.warnings);
            }

            // 处理物模型数据
            if (processedData.properties && Object.keys(processedData.properties).length > 0) {
                this.updateElectricalParametersFromProcessedData(processedData.properties);
                console.log(`成功更新 ${Object.keys(processedData.properties).length} 个参数`);
            } else {
                console.warn('处理后的数据中没有有效的属性');
            }

            // 触发数据更新回调
            this.triggerDataUpdateCallbacks(processedData, metadata);

            // 更新图表数据
            if (typeof updateElectricalDataForCharts === 'function') {
                updateElectricalDataForCharts(processedData);
                console.log('已更新图表数据');
            } else {
                console.warn('图表更新函数未找到');
            }

            // 记录数据处理事件
            logDataEvent({ properties: processedData.properties, metadata: metadata });

            console.log('电气系统数据处理完成');

            // 记录处理统计（每10次记录一次）
            const stats = this.dataProcessor.getStatistics();
            if (stats.totalReceived % 10 === 0) {
                console.log(`数据处理统计: 总计${stats.totalReceived}次，成功率${stats.successRate.toFixed(1)}%`);
            }

        } catch (error) {
            console.error('处理电气系统数据时发生严重错误:', error);
            console.error('错误堆栈:', error.stack);

            // 确保错误不影响其他功能
            this.handleCriticalError(error);
        }
    }

    /**
     * 验证接收到的数据
     * @param {Object} data - 要验证的数据
     * @returns {boolean} 验证结果
     */
    validateIncomingData(data) {
        if (!data || typeof data !== 'object') {
            console.error('数据不是有效的对象');
            return false;
        }

        // 检查是否有 message 数组或 properties 对象
        if (!data.message && !data.properties) {
            console.error('数据中缺少 message 数组或 properties 对象');
            return false;
        }

        // 如果有 message 数组，检查其格式
        if (data.message) {
            if (!Array.isArray(data.message)) {
                console.error('message 字段不是数组');
                return false;
            }

            if (data.message.length === 0) {
                console.warn('message 数组为空');
                return false;
            }
        }

        return true;
    }

    /**
     * 处理数据处理错误
     * @param {string} error - 错误信息
     */
    handleDataProcessingError(error) {
        console.warn('数据处理错误，保持当前界面状态');

        // 记录错误事件
        logErrorEvent(error, '数据处理错误');

        // 可以在这里添加错误恢复逻辑
        // 例如：显示错误提示，但不清空现有数据

        // 更新时间戳显示错误状态
        updateDataTimestamp(null, '数据处理错误');
    }

    /**
     * 处理严重错误
     * @param {Error} error - 错误对象
     */
    handleCriticalError(error) {
        console.error('发生严重错误，尝试恢复连接');

        // 记录严重错误事件
        logErrorEvent(error.message, '严重错误 - 尝试恢复连接');

        // 重置连接状态
        this.isConnected = false;

        // 尝试重新连接
        setTimeout(() => {
            console.log('尝试从严重错误中恢复...');
            logConnectionEvent('recovery_attempt', '从严重错误中恢复');
            this.scheduleReconnect();
        }, 5000);
    }

    /**
     * 处理已处理的电气系统参数数据
     * @param {Object} processedProperties - 已处理的属性数据
     */
    updateElectricalParametersFromProcessedData(processedProperties) {
        Object.keys(processedProperties).forEach(propertyId => {
            const propertyData = processedProperties[propertyId];
            const mapping = this.parameterMapping[propertyId];

            if (mapping && propertyData.isValid) {
                this.updateParameterDisplay(mapping, propertyData.value, propertyData);
                console.log(`更新参数 ${propertyData.name}: ${propertyData.value} ${propertyData.unit}`);
            } else if (!propertyData.isValid) {
                console.warn(`参数 ${propertyData.name} 数据无效:`, propertyData.value);
            }
        });
    }

    /**
     * 验证数据格式（保留用于兼容性，实际验证由数据处理器完成）
     * @param {Object} data - 要验证的数据
     * @returns {boolean} 验证结果
     */
    validateDataFormat(data) {
        // 基本格式验证
        if (!data || typeof data !== 'object') {
            return false;
        }

        // 检查是否包含预期的数据结构
        return data.properties || data.functions || data.events ||
            Object.keys(data).some(key => this.parameterMapping[key]);
    }

    /**
     * 更新电气系统参数显示
     * @param {Object} properties - 属性数据
     */
    updateElectricalParameters(properties) {
        Object.keys(properties).forEach(propertyId => {
            const mapping = this.parameterMapping[propertyId];
            if (mapping) {
                const value = properties[propertyId];
                this.updateParameterDisplay(mapping, value);
            }
        });
    }

    /**
     * 更新参数显示
     * @param {Object} mapping - 参数映射信息
     * @param {*} value - 参数值
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateParameterDisplay(mapping, value, propertyData = null) {
        const element = document.getElementById(mapping.elementId);
        if (element) {
            switch (mapping.type) {
                case 'status':
                    this.updateStatusIndicator(element, value, mapping.name, propertyData);
                    break;
                case 'value':
                    const unit = propertyData ? propertyData.unit : (mapping.unit || '');
                    this.updateValueDisplay(element, value, unit, propertyData);
                    break;
                default:
                    element.textContent = value;
                    if (propertyData) {
                        element.title = `${propertyData.name}: ${value} ${propertyData.unit} (更新时间: ${new Date(propertyData.processedAt).toLocaleTimeString()})`;
                    }
            }
        } else {
            // 如果没有找到对应的元素，尝试更新通用的电气参数显示
            this.updateGenericElectricalDisplay(mapping.name, value, propertyData);
        }
    }

    /**
     * 更新状态指示器
     * @param {HTMLElement} element - DOM 元素
     * @param {*} value - 状态值
     * @param {string} name - 参数名称
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateStatusIndicator(element, value, name, propertyData = null) {
        const isActive = value > 0;
        element.classList.remove('active', 'inactive');
        element.classList.add(isActive ? 'active' : 'inactive');

        // 更新状态文本
        const statusText = element.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = `${name}: ${isActive ? '激活' : '未激活'}`;
        }

        // 添加详细信息到 title 属性
        if (propertyData) {
            const updateTime = new Date(propertyData.processedAt).toLocaleTimeString();
            element.title = `${name}: ${isActive ? '激活' : '未激活'} (值: ${value}, 更新时间: ${updateTime})`;
        }

        console.log(`状态更新 - ${name}: ${isActive ? '激活' : '未激活'} (原始值: ${value})`);
    }

    /**
     * 更新数值显示
     * @param {HTMLElement} element - DOM 元素
     * @param {*} value - 数值
     * @param {string} unit - 单位
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateValueDisplay(element, value, unit, propertyData = null) {
        const formattedValue = typeof value === 'number' ? value.toFixed(2) : value;
        element.textContent = `${formattedValue} ${unit}`;

        // 根据数据有效性设置颜色
        if (propertyData) {
            if (propertyData.isValid && value !== null && value !== undefined && value !== '等待数据...') {
                // 有效数据显示正常颜色（白色）
                element.style.color = '#ffffff';
            } else {
                // 无效数据或缺失数据显示红色警告
                element.style.color = '#ff4444';
            }

            // 添加详细信息到 title 属性
            const updateTime = new Date(propertyData.processedAt).toLocaleTimeString();
            element.title = `${propertyData.name}: ${formattedValue} ${unit} (更新时间: ${updateTime})`;
        } else {
            // 如果没有属性数据，检查值是否为等待状态
            if (value === '等待数据...' || value === null || value === undefined) {
                element.style.color = '#ff4444';
            } else {
                element.style.color = '#ffffff';
            }
        }
    }

    /**
     * 更新通用电气参数显示
     * @param {string} paramName - 参数名称
     * @param {*} value - 参数值
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateGenericElectricalDisplay(paramName, value, propertyData = null) {
        // 尝试更新现有的电气参数显示元素
        const possibleIds = [
            `${paramName.toLowerCase().replace(/\s+/g, '-')}-value`,
            `electrical-${paramName.toLowerCase().replace(/\s+/g, '-')}`,
            `param-${paramName.toLowerCase().replace(/\s+/g, '-')}`
        ];

        for (const id of possibleIds) {
            const element = document.getElementById(id);
            if (element) {
                const displayValue = propertyData ?
                    `${value} ${propertyData.unit}` : value;
                element.textContent = displayValue;

                if (propertyData) {
                    const updateTime = new Date(propertyData.processedAt).toLocaleTimeString();
                    this.updateParameterTooltip(element, `${propertyData.name}: ${value} ${propertyData.unit} (更新时间: ${updateTime})`);
                }

                console.log(`通用参数更新 - ${paramName}: ${displayValue}`);
                break;
            }
        }
    }

    /**
     * 添加数据更新回调
     * @param {Function} callback - 回调函数
     */
    addDataUpdateCallback(callback) {
        if (typeof callback === 'function') {
            this.dataUpdateCallbacks.push(callback);
        }
    }

    /**
     * 触发数据更新回调
     * @param {Object} data - 更新的数据
     * @param {Object} metadata - 数据元信息（可选）
     */
    triggerDataUpdateCallbacks(data, metadata = null) {
        this.dataUpdateCallbacks.forEach(callback => {
            try {
                callback(data, metadata);
            } catch (error) {
                console.error('数据更新回调执行失败:', error);
            }
        });
    }

    /**
     * 获取 token（简化版本，实际应该从认证模块获取）
     */
    getToken() {
        // 这里应该实现实际的 token 获取逻辑
        // 暂时返回 null，让系统使用 share 参数
        return null;
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        const processingStats = this.dataProcessor.getStatistics();
        return {
            isConnected: this.isConnected,
            lastDataUpdate: this.lastDataUpdate,
            reconnectAttempts: this.reconnectAttempts,
            dataProcessing: {
                totalReceived: processingStats.totalReceived,
                validData: processingStats.validData,
                invalidData: processingStats.invalidData,
                successRate: processingStats.successRate,
                lastProcessTime: processingStats.lastProcessTime
            }
        };
    }

    /**
     * 获取数据处理统计信息
     */
    getDataProcessingStatistics() {
        return this.dataProcessor.getStatistics();
    }

    /**
     * 获取支持的参数列表
     */
    getSupportedParameters() {
        return this.dataProcessor.getSupportedProperties();
    }

    /**
     * 更新参数项的 tooltip
     * @param {HTMLElement} element - 目标元素
     * @param {string} tooltipText - tooltip 文本内容
     */
    updateParameterTooltip(element, tooltipText) {
        // 检查元素是否为 parameter-item 或其子元素
        const parameterItem = element.closest('.parameter-item') ||
            (element.classList.contains('parameter-item') ? element : null);

        if (!parameterItem) {
            // 如果不是 parameter-item，则直接设置 title 属性（用于其他元素）
            element.title = tooltipText;
            return;
        }

        // 查找或创建 tooltip 元素
        let tooltip = parameterItem.querySelector('.parameter-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.className = 'parameter-tooltip';
            parameterItem.appendChild(tooltip);

            // 标记已初始化，避免重复绑定事件
            parameterItem.setAttribute('data-tooltip-initialized', 'true');

            // 初始化 tooltip 管理器
            initializeParameterTooltipBehavior(parameterItem, tooltip);
        }

        // 更新 tooltip 内容
        tooltip.textContent = tooltipText;
    }

    /**
     * 重置数据处理统计
     */
    resetDataProcessingStatistics() {
        this.dataProcessor.resetStatistics();
    }

    /**
     * 手动重连
     */
    async reconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
        }
        this.reconnectAttempts = 0;
        await this.init();
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
            this.mqttClient = null;
        }
        this.isConnected = false;
        console.log('MQTT 电气系统连接已断开');
    }
}

// ==================== MQTT 水冷系统数据管理器 ====================

/**
 * MQTT 水冷系统数据管理器类
 */
class MQTTCoolingDataManager {
    constructor() {
        this.mqttClient = null;
        this.isConnected = false;
        this.subscriptionTopic = '/185/D19YFA0X66T51/ws/service';
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5秒重连间隔
        this.dataUpdateCallbacks = [];
        this.lastDataUpdate = null;

        // 初始化数据处理器（复用电气系统的处理器）
        this.dataProcessor = new ElectricalDataProcessor();

        // 水冷系统参数映射表
        this.parameterMapping = {
            // 状态参数映射
            'HMI_33525_0': { name: '水冷系统自动模式', elementId: 'cooling-auto-mode', type: 'status' },
            'HMI_33525_2': { name: '水冷系统远程控制', elementId: 'cooling-remote-control', type: 'status' },
            'HMI_33525_10': { name: '水冷运行状态', elementId: 'cooling-running-status', type: 'status' },

            // 温度参数映射
            'HMI_33500': { name: '供水温度', elementId: 'cooling-supply-temp', type: 'value', unit: '℃' },
            'HMI_33501': { name: '回水温度', elementId: 'cooling-return-temp', type: 'value', unit: '℃' },
            'HMI_33506': { name: '阀室温度', elementId: 'cooling-valve-temp', type: 'value', unit: '℃' },

            // 压力参数映射
            'HMI_33502': { name: '供水压力', elementId: 'cooling-supply-pressure', type: 'value', unit: 'bar' },
            'HMI_33503': { name: '回水压力', elementId: 'cooling-return-pressure', type: 'value', unit: 'bar' },

            // 流量和其他参数映射
            'HMI_33504': { name: '冷却水流量', elementId: 'cooling-flow-rate', type: 'value', unit: 'L/min' },
            'HMI_33505': { name: '冷却水电导率', elementId: 'cooling-conductivity', type: 'value', unit: 'μs/cm' }
        };

        this.init();
    }

    /**
     * 初始化 MQTT 连接
     */
    async init() {
        try {
            console.log('初始化 MQTT 水冷系统数据管理器...');
            await this.connectMQTT();
            await this.subscribeToTopic();
            console.log('MQTT 水冷系统数据管理器初始化完成');
        } catch (error) {
            console.error('MQTT 水冷系统初始化失败:', error);
            this.reconnectAttempts++;
            this.scheduleReconnect();
        }
    }

    /**
     * 连接 MQTT 服务器
     */
    async connectMQTT() {
        return new Promise((resolve, reject) => {
            try {
                // 使用与电气系统相同的 MQTT 配置
                const options = {
                    username: 'FastBee',
                    password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.1O79OeRWpHjRa5nps9L9ETtplMTLmUiiZpmLi-waRkGnM4M72fgWdj4QGxqx_-ccCedyixw5jpfeWtniY5RHXQ',
                    cleanSession: true,
                    keepAlive: 30,
                    clientId: 'web-' + Math.random().toString(16).substr(2),
                    connectTimeout: 60000,
                };

                // 使用相同的 MQTT 服务器地址
                const url = 'ws://*************:8083/mqtt';
                console.log('连接到 MQTT 服务器（水冷系统）：', url);

                this.mqttClient = mqtt.connect(url, options);

                this.mqttClient.on('connect', () => {
                    console.log('MQTT 水冷系统连接成功');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;

                    // 记录连接事件
                    logConnectionEvent('cooling_connected', '水冷系统成功连接到 MQTT 服务器');

                    resolve();
                });

                this.mqttClient.on('error', (error) => {
                    console.error('MQTT 水冷系统连接失败:', error);
                    this.isConnected = false;

                    // 记录错误事件
                    logErrorEvent(error.message, 'MQTT 水冷系统连接失败');

                    reject(error);
                });

                this.mqttClient.on('close', () => {
                    console.log('MQTT 水冷系统连接已断开');
                    this.isConnected = false;

                    // 记录连接事件
                    logConnectionEvent('cooling_disconnected', '水冷系统连接已断开');
                });

            } catch (error) {
                console.error('创建 MQTT 水冷系统连接时发生错误:', error);
                reject(error);
            }
        });
    }

    /**
     * 订阅主题
     */
    async subscribeToTopic() {
        return new Promise((resolve, reject) => {
            if (!this.mqttClient || !this.isConnected) {
                reject(new Error('MQTT 水冷系统客户端未连接'));
                return;
            }

            this.mqttClient.subscribe(this.subscriptionTopic, { qos: 1 }, (err) => {
                if (err) {
                    console.error('订阅水冷系统主题失败:', err);
                    reject(err);
                } else {
                    console.log('成功订阅水冷系统主题:', this.subscriptionTopic);

                    // 设置消息处理器
                    this.mqttClient.on('message', (topic, message) => {
                        this.handleMessage(topic, message);
                    });

                    resolve();
                }
            });
        });
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(topic, message) {
        try {
            const data = JSON.parse(message.toString());
            console.log('收到水冷系统 MQTT 消息:', data);

            this.processCoolingData(data);
        } catch (error) {
            console.error('解析水冷系统 MQTT 消息失败:', error);
            logErrorEvent(error.message, '水冷系统消息解析失败');
        }
    }

    /**
     * 处理水冷系统数据
     * @param {Object} data - 接收到的数据
     */
    processCoolingData(data) {
        try {
            console.log('开始处理水冷系统数据');

            // 基本数据验证
            if (!this.validateIncomingData(data)) {
                console.error('接收到的水冷系统数据格式无效，跳过处理');
                return;
            }

            // 使用数据处理器处理数据
            const processingResult = this.dataProcessor.processData(data);

            if (!processingResult.success) {
                console.error('水冷系统数据处理失败:', processingResult.error);
                if (processingResult.details) {
                    console.error('详细信息:', processingResult.details);
                }

                this.handleDataProcessingError(processingResult.error);
                return;
            }

            const processedData = processingResult.data;
            const metadata = processingResult.metadata;

            // 更新最后数据时间
            this.lastDataUpdate = new Date();

            // 记录数据质量信息
            console.log(`水冷系统数据质量评估: ${metadata.dataQuality.score.toFixed(1)}% (${metadata.dataQuality.level})`);

            if (metadata.integrityCheck.warnings.length > 0) {
                console.warn('水冷系统数据完整性警告:', metadata.integrityCheck.warnings);
            }

            // 处理水冷系统参数
            if (processedData.properties && Object.keys(processedData.properties).length > 0) {
                this.updateCoolingParametersFromProcessedData(processedData.properties);
                console.log(`成功更新 ${Object.keys(processedData.properties).length} 个水冷系统参数`);
            } else {
                console.warn('处理后的水冷系统数据中没有有效的属性');
            }

            // 触发数据更新回调
            this.triggerDataUpdateCallbacks(processedData, metadata);

            console.log('水冷系统数据处理完成');

            // 记录数据处理事件
            logDataEvent({ properties: processedData.properties, metadata: metadata, system: 'cooling' });

        } catch (error) {
            console.error('处理水冷系统数据时发生严重错误:', error);
            console.error('错误堆栈:', error.stack);

            this.handleCriticalError(error);
        }
    }

    /**
     * 使用处理后的数据更新水冷系统参数
     * @param {Object} processedProperties - 已处理的属性数据
     */
    updateCoolingParametersFromProcessedData(processedProperties) {
        Object.keys(processedProperties).forEach(propertyId => {
            const propertyData = processedProperties[propertyId];
            const mapping = this.parameterMapping[propertyId];

            if (mapping && propertyData.isValid) {
                this.updateParameterDisplay(mapping, propertyData.value, propertyData);
                console.log(`更新水冷系统参数 ${propertyData.name}: ${propertyData.value} ${propertyData.unit}`);
            } else if (mapping && !propertyData.isValid) {
                console.warn(`水冷系统参数 ${propertyData.name} 数据无效:`, propertyData.value);
            } else if (!mapping) {
                console.log(`未映射的水冷系统参数: ${propertyData.name} (${propertyId}) = ${propertyData.value} ${propertyData.unit}`);
            }
        });
    }

    /**
     * 更新参数显示
     * @param {Object} mapping - 参数映射信息
     * @param {*} value - 参数值
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateParameterDisplay(mapping, value, propertyData = null) {
        const element = document.getElementById(mapping.elementId);
        if (element) {
            if (mapping.type === 'status') {
                this.updateStatusIndicator(element, value, mapping.name, propertyData);
            } else if (mapping.type === 'value') {
                const unit = propertyData ? propertyData.unit : (mapping.unit || '');
                this.updateValueDisplay(element, value, unit, propertyData);
            }
        } else {
            // 如果没有找到对应的元素，尝试更新通用的水冷参数显示
            this.updateGenericCoolingDisplay(mapping.name, value, propertyData);
        }
    }

    /**
     * 更新状态指示器
     * @param {HTMLElement} element - DOM 元素
     * @param {*} value - 状态值
     * @param {string} name - 参数名称
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateStatusIndicator(element, value, name, propertyData = null) {
        // 对于水冷系统，根据参数类型确定显示文本
        let displayText = '';

        switch (name) {
            case '水冷系统自动模式':
                displayText = value > 0 ? '启用' : '禁用';
                break;
            case '水冷系统远程控制':
                displayText = value > 0 ? '启用' : '禁用';
                break;
            case '水冷运行状态':
                displayText = value > 0 ? '正常' : '异常';
                break;
            default:
                displayText = value > 0 ? '激活' : '未激活';
        }

        element.textContent = displayText;

        // 添加详细信息到 tooltip
        if (propertyData) {
            const updateTime = new Date(propertyData.processedAt).toLocaleTimeString();
            this.updateParameterTooltip(element, `${name}: ${displayText} (值: ${value}, 更新时间: ${updateTime})`);
        }

        console.log(`水冷系统状态更新 - ${name}: ${displayText} (原始值: ${value})`);
    }

    /**
     * 更新数值显示
     * @param {HTMLElement} element - DOM 元素
     * @param {*} value - 数值
     * @param {string} unit - 单位
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateValueDisplay(element, value, unit, propertyData = null) {
        const formattedValue = typeof value === 'number' ? value.toFixed(1) : value;
        element.textContent = `${formattedValue} ${unit}`;

        // 根据数据有效性设置颜色
        if (propertyData) {
            if (propertyData.isValid && value !== null && value !== undefined && value !== '等待数据...') {
                // 有效数据显示正常颜色（白色）
                element.style.color = '#ffffff';
            } else {
                // 无效数据或缺失数据显示红色警告
                element.style.color = '#ff4444';
            }

            // 添加详细信息到 tooltip
            const updateTime = new Date(propertyData.processedAt).toLocaleTimeString();
            this.updateParameterTooltip(element, `${propertyData.name}: ${formattedValue} ${unit} (更新时间: ${updateTime})`);
        } else {
            // 如果没有属性数据，检查值是否为等待状态
            if (value === '等待数据...' || value === null || value === undefined) {
                element.style.color = '#ff4444';
            } else {
                element.style.color = '#ffffff';
            }
        }
    }

    /**
     * 更新通用水冷参数显示
     * @param {string} paramName - 参数名称
     * @param {*} value - 参数值
     * @param {Object} propertyData - 完整的属性数据（可选）
     */
    updateGenericCoolingDisplay(paramName, value, propertyData = null) {
        console.log(`通用水冷系统参数更新 - ${paramName}: ${value} ${propertyData ? propertyData.unit : ''}`);
    }

    // 复用电气系统的验证和错误处理方法
    validateIncomingData(data) {
        if (!data || typeof data !== 'object') {
            console.error('水冷系统数据不是有效的对象');
            return false;
        }

        if (!data.message && !data.properties) {
            console.error('水冷系统数据中缺少 message 数组或 properties 对象');
            return false;
        }

        if (data.message) {
            if (!Array.isArray(data.message)) {
                console.error('水冷系统 message 字段不是数组');
                return false;
            }

            if (data.message.length === 0) {
                console.warn('水冷系统 message 数组为空');
                return false;
            }
        }

        return true;
    }

    handleDataProcessingError(error) {
        console.warn('水冷系统数据处理错误，保持当前界面状态');
        logErrorEvent(error, '水冷系统数据处理错误');
    }

    handleCriticalError(error) {
        console.error('水冷系统发生严重错误，尝试恢复连接');
        logErrorEvent(error.message, '水冷系统严重错误 - 尝试恢复连接');

        this.isConnected = false;

        setTimeout(() => {
            console.log('尝试从水冷系统严重错误中恢复...');
            logConnectionEvent('cooling_recovery_attempt', '从水冷系统严重错误中恢复');
            this.scheduleReconnect();
        }, 5000);
    }

    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error(`水冷系统 MQTT 重连次数超过限制 (${this.maxReconnectAttempts})，停止自动重连`);
            console.log('您可以手动调用 reconnectCoolingMQTT() 函数进行重连');
            return;
        }

        const backoffDelay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts), 60000);

        console.log(`第 ${this.reconnectAttempts + 1} 次尝试重连水冷系统 MQTT，${backoffDelay / 1000}秒后重试...`);

        setTimeout(() => {
            this.init();
        }, backoffDelay);
    }

    addDataUpdateCallback(callback) {
        this.dataUpdateCallbacks.push(callback);
    }

    triggerDataUpdateCallbacks(data, metadata = null) {
        this.dataUpdateCallbacks.forEach(callback => {
            try {
                callback(data, metadata);
            } catch (error) {
                console.error('水冷系统数据更新回调执行失败:', error);
            }
        });
    }

    async reconnect() {
        console.log('手动重连水冷系统 MQTT...');
        this.reconnectAttempts = 0;
        await this.init();
    }

    disconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
            this.mqttClient = null;
        }
        this.isConnected = false;
        console.log('水冷系统 MQTT 连接已断开');
    }

    getConnectionStatus() {
        const processingStats = this.dataProcessor.getStatistics();
        return {
            isConnected: this.isConnected,
            lastDataUpdate: this.lastDataUpdate,
            reconnectAttempts: this.reconnectAttempts,
            system: 'cooling',
            topic: this.subscriptionTopic,
            dataProcessing: {
                totalReceived: processingStats.totalReceived,
                validData: processingStats.validData,
                invalidData: processingStats.invalidData,
                successRate: processingStats.successRate,
                lastProcessTime: processingStats.lastProcessTime
            }
        };
    }

    getDataProcessingStatistics() {
        return this.dataProcessor.getStatistics();
    }

    getSupportedParameters() {
        return this.dataProcessor.getSupportedProperties().filter(prop =>
            this.dataProcessor.dataSchema.properties[prop.id] &&
            this.dataProcessor.dataSchema.properties[prop.id].system === 'cooling'
        );
    }

    resetDataProcessingStatistics() {
        this.dataProcessor.resetStatistics();
    }

    /**
     * 更新参数项的 tooltip
     * @param {HTMLElement} element - 目标元素
     * @param {string} tooltipText - tooltip 文本内容
     */
    updateParameterTooltip(element, tooltipText) {
        // 检查元素是否为 parameter-item 或其子元素
        const parameterItem = element.closest('.parameter-item') ||
            (element.classList.contains('parameter-item') ? element : null);

        if (!parameterItem) {
            // 如果不是 parameter-item，则直接设置 title 属性（用于其他元素）
            element.title = tooltipText;
            return;
        }

        // 查找或创建 tooltip 元素
        let tooltip = parameterItem.querySelector('.parameter-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.className = 'parameter-tooltip';
            parameterItem.appendChild(tooltip);

            // 标记已初始化，避免重复绑定事件
            parameterItem.setAttribute('data-tooltip-initialized', 'true');

            // 初始化 tooltip 管理器
            initializeParameterTooltipBehavior(parameterItem, tooltip);
        }

        // 更新 tooltip 内容
        tooltip.textContent = tooltipText;
    }
}

// 全局 MQTT 数据管理器实例
let mqttElectricalManager = null;
let mqttCoolingManager = null;

// 调试和监控变量
let debugMode = false;
let connectionHistory = [];
let dataHistory = [];
let errorHistory = [];

// Tooltip 调试和恢复机制
let tooltipDebugMode = false;
let tooltipRecoveryInterval = null;

/**
 * 初始化 MQTT 电气系统数据功能
 */
function initMQTTElectricalData() {
    console.log('初始化 MQTT 电气系统数据功能...');

    try {
        mqttElectricalManager = new MQTTElectricalDataManager();

        // 添加数据更新回调，用于更新现有的电气参数显示
        mqttElectricalManager.addDataUpdateCallback((data) => {
            // 更新现有的电气系统参数显示
            updateElectricalParametersFromMQTT(data);
        });

        console.log('MQTT 电气系统数据功能初始化完成');
    } catch (error) {
        console.error('MQTT 电气系统数据功能初始化失败:', error);
    }
}

/**
 * 初始化 MQTT 水冷系统数据功能
 */
function initMQTTCoolingData() {
    console.log('初始化 MQTT 水冷系统数据功能...');

    try {
        mqttCoolingManager = new MQTTCoolingDataManager();

        // 添加数据更新回调，用于更新现有的水冷参数显示
        mqttCoolingManager.addDataUpdateCallback((data) => {
            // 更新现有的水冷系统参数显示
            updateCoolingParametersFromMQTT(data);
        });

        console.log('MQTT 水冷系统数据功能初始化完成');
    } catch (error) {
        console.error('MQTT 水冷系统数据功能初始化失败:', error);
    }
}

/**
 * 从 MQTT 数据更新电气参数显示
 * @param {Object} data - MQTT 接收到的数据
 * @param {Object} metadata - 数据元信息（可选）
 */
function updateElectricalParametersFromMQTT(data, metadata = null) {
    console.log('从 MQTT 数据更新电气参数显示');

    // 调用更新函数，传入 MQTT 数据
    updateElectricalParameters(data);

    // 更新界面上的数据质量指示器
    if (metadata && metadata.dataQuality) {
        updateDataQualityIndicator(metadata.dataQuality, 'electrical');
    }

    // 更新连接状态显示
    updateConnectionStatusDisplay();

    // 更新系统状态概览
    updateSystemStatusOverview(data, metadata);
}

/**
 * 从 MQTT 数据更新水冷参数显示
 * @param {Object} data - MQTT 接收到的数据
 * @param {Object} metadata - 数据元信息（可选）
 */
function updateCoolingParametersFromMQTT(data, metadata = null) {
    console.log('从 MQTT 数据更新水冷参数显示');

    // 调用更新函数，传入 MQTT 数据
    updateCoolingParameters(data);

    // 更新界面上的数据质量指示器
    if (metadata && metadata.dataQuality) {
        updateDataQualityIndicator(metadata.dataQuality, 'cooling');
    }

    // 更新连接状态显示
    updateConnectionStatusDisplay();

    // 更新水冷系统状态概览
    updateCoolingSystemStatusOverview(data, metadata);
}

/**
 * 更新数据质量指示器
 * @param {Object} dataQuality - 数据质量信息
 * @param {string} system - 系统类型 ('electrical' 或 'cooling')
 */
function updateDataQualityIndicator(dataQuality, system = 'electrical') {
    const qualityElementId = system === 'cooling' ? 'cooling-data-quality-indicator' : 'data-quality-indicator';
    const qualityElement = document.getElementById(qualityElementId);
    const systemName = system === 'cooling' ? '水冷' : '电气';

    if (qualityElement) {
        qualityElement.className = `data-quality-indicator ${dataQuality.level}`;
        qualityElement.textContent = `${systemName}数据质量: ${dataQuality.score.toFixed(1)}%`;
        qualityElement.title = `${systemName}系统质量等级: ${dataQuality.level}\n有效参数: ${dataQuality.validProperties}/${dataQuality.totalProperties}`;

        if (dataQuality.issues.length > 0) {
            qualityElement.title += `\n问题: ${dataQuality.issues.join(', ')}`;
        }
    } else {
        // 如果没有专门的质量指示器，在控制台显示
        console.log(`${systemName}系统数据质量: ${dataQuality.score.toFixed(1)}% (${dataQuality.level})`);
    }
}

/**
 * 更新连接状态显示
 */
function updateConnectionStatusDisplay() {
    const status = getMQTTConnectionStatus();

    // 更新通用 MQTT 连接状态
    const statusElements = document.querySelectorAll('.mqtt-connection-status');
    statusElements.forEach(element => {
        if (status.overall.isConnected) {
            element.classList.remove('disconnected');
            element.classList.add('connected');
            element.textContent = `MQTT 已连接 (${status.overall.connectedSystems}/${status.overall.totalSystems})`;
        } else {
            element.classList.remove('connected');
            element.classList.add('disconnected');
            element.textContent = 'MQTT 未连接';
        }
    });

    // 更新电气系统连接状态
    const electricalStatusElements = document.querySelectorAll('.mqtt-electrical-status');
    electricalStatusElements.forEach(element => {
        if (status.electrical && status.electrical.isConnected) {
            element.classList.remove('disconnected');
            element.classList.add('connected');
            element.textContent = '电气系统已连接';
        } else {
            element.classList.remove('connected');
            element.classList.add('disconnected');
            element.textContent = '电气系统未连接';
        }
    });

    // 更新水冷系统连接状态
    const coolingStatusElements = document.querySelectorAll('.mqtt-cooling-status');
    coolingStatusElements.forEach(element => {
        if (status.cooling && status.cooling.isConnected) {
            element.classList.remove('disconnected');
            element.classList.add('connected');
            element.textContent = '水冷系统已连接';
        } else {
            element.classList.remove('connected');
            element.classList.add('disconnected');
            element.textContent = '水冷系统未连接';
        }
    });
}

/**
 * 更新系统状态概览
 * @param {Object} data - 系统数据
 * @param {Object} metadata - 数据元信息
 */
function updateSystemStatusOverview(data, metadata) {
    // 检查系统状态（不再使用计数器）
    let hasFault = false;
    let hasRunning = false;
    let hasReady = false;
    let hasStandby = false;

    if (data.properties) {
        Object.keys(data.properties).forEach(propertyId => {
            const property = data.properties[propertyId];
            const value = property.value;

            switch (propertyId) {
                case 'HMI_30039_5': // 运行
                    if (value > 0) hasRunning = true;
                    break;
                case 'HMI_30039_6': // 故障
                case 'HMI_30039_11': // 故障2
                    if (value > 0) hasFault = true;
                    break;
                case 'HMI_30039_4': // 就绪
                    if (value > 0) hasReady = true;
                    break;
                case 'HMI_30039_9': // 备用
                    if (value > 0) hasStandby = true;
                    break;
            }
        });
    }

    // 更新整体系统状态（优先级：故障 > 运行 > 就绪 > 备用 > 待机）
    const overallStatus = hasFault ? 'fault' :
        hasRunning ? 'running' :
            hasReady ? 'ready' :
                hasStandby ? 'standby' : 'standby';

    updateOverallSystemStatus(overallStatus);
}



/**
 * 更新整体系统状态
 * @param {string} status - 系统状态
 */
function updateOverallSystemStatus(status) {
    const statusElement = document.getElementById('overall-system-status');
    if (statusElement) {
        statusElement.className = `system-status ${status}`;

        const statusText = {
            'running': '运行中',
            'fault': '故障',
            'ready': '就绪',
            'standby': '待机'
        };

        statusElement.textContent = statusText[status] || '未知';
    }
}

/**
 * 更新状态显示
 * @param {string} elementId - 元素ID
 * @param {boolean} isActive - 是否激活
 */
function updateStatusDisplay(elementId, isActive) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove('active', 'inactive');
        element.classList.add(isActive ? 'active' : 'inactive');

        // 更新状态文本
        const statusText = element.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = isActive ? '激活' : '未激活';
        }

        console.log(`状态更新 - ${elementId}: ${isActive ? '激活' : '未激活'}`);
    } else {
        // 如果找不到具体的状态元素，尝试更新通用指示器
        updateGenericStatusIndicator(elementId, isActive);
    }
}

/**
 * 更新数值显示
 * @param {string} elementId - 元素ID
 * @param {number} value - 数值
 * @param {string} unit - 单位
 * @param {Object} propertyData - 完整的属性数据（可选）
 */
function updateValueDisplay(elementId, value, unit, propertyData = null) {
    const element = document.getElementById(elementId);
    if (element) {
        const formattedValue = typeof value === 'number' ? value.toFixed(2) : value;
        element.textContent = `${formattedValue} ${unit}`;

        // 根据数据有效性设置颜色
        if (propertyData) {
            if (propertyData.isValid && value !== null && value !== undefined && value !== '等待数据...') {
                // 有效数据显示正常颜色（白色）
                element.style.color = '#ffffff';
            } else {
                // 无效数据或缺失数据显示红色警告
                element.style.color = '#ff4444';
            }
        } else {
            // 如果没有属性数据，检查值是否为等待状态
            if (value === '等待数据...' || value === null || value === undefined) {
                element.style.color = '#ff4444';
            } else {
                element.style.color = '#ffffff';
            }
        }

        console.log(`数值更新 - ${elementId}: ${formattedValue} ${unit}`);
    } else {
        console.log(`未找到元素 ${elementId}，数值: ${value} ${unit}`);
    }
}

/**
 * 更新通用状态指示器
 * @param {string} statusName - 状态名称
 * @param {boolean} isActive - 是否激活
 */
function updateGenericStatusIndicator(statusName, isActive) {
    // 查找可能的状态指示器元素
    const possibleSelectors = [
        `.status-indicator[data-status="${statusName}"]`,
        `.indicator-dot[data-status="${statusName}"]`,
        `#${statusName}`,
        `.${statusName}-indicator`
    ];

    for (const selector of possibleSelectors) {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            elements.forEach(element => {
                element.classList.remove('active', 'inactive');
                element.classList.add(isActive ? 'active' : 'inactive');
            });
            console.log(`通用状态更新 - ${statusName}: ${isActive ? '激活' : '未激活'}`);
            break;
        }
    }
}

/**
 * 更新数据时间戳显示
 * @param {Date|null} timestamp - 时间戳
 * @param {string} customMessage - 自定义消息
 */
function updateDataTimestamp(timestamp, customMessage = null) {
    const timestampElements = document.querySelectorAll('.data-timestamp, #data-timestamp');

    let displayText;
    if (customMessage) {
        displayText = customMessage;
    } else if (timestamp) {
        const formattedTime = timestamp.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        displayText = `数据更新时间: ${formattedTime}`;
    } else {
        displayText = '数据更新时间: --';
    }

    timestampElements.forEach(element => {
        element.textContent = displayText;

        // 根据状态设置颜色
        if (customMessage) {
            if (customMessage.includes('断开')) {
                element.style.color = '#f44336';
            } else if (customMessage.includes('等待')) {
                element.style.color = '#ff9800';
            } else {
                element.style.color = '#666';
            }
        } else {
            element.style.color = '#4caf50'; // 正常数据为绿色
        }
    });

    // 如果没有专门的时间戳元素，在控制台显示
    if (timestampElements.length === 0) {
        console.log(displayText);
    }
}

/**
 * 添加 MQTT 连接状态指示器
 */
function addMQTTStatusIndicator() {
    console.log('初始化 MQTT 连接状态指示器');

    // 定期更新页面中的 MQTT 状态指示器
    setInterval(() => {
        updateMQTTConnectionStatusDisplay();
    }, 1000);

    // 立即更新一次
    updateMQTTConnectionStatusDisplay();
}

/**
 * 更新 MQTT 连接状态显示
 */
function updateMQTTConnectionStatusDisplay() {
    const status = getMQTTConnectionStatus();

    // 更新头部的 MQTT 状态指示器
    const mqttStatusElement = document.getElementById('mqtt-status');
    if (mqttStatusElement) {
        if (status.overall && status.overall.isConnected) {
            mqttStatusElement.className = 'mqtt-connection-status connected';
            mqttStatusElement.textContent = `MQTT 已连接 (${status.overall.connectedSystems}/${status.overall.totalSystems})`;
        } else {
            mqttStatusElement.className = 'mqtt-connection-status disconnected';

            // 获取重连次数（优先显示电气系统的重连次数）
            let reconnectAttempts = 0;
            if (status.electrical && status.electrical.reconnectAttempts !== undefined) {
                reconnectAttempts = status.electrical.reconnectAttempts;
            } else if (status.cooling && status.cooling.reconnectAttempts !== undefined) {
                reconnectAttempts = status.cooling.reconnectAttempts;
            }

            mqttStatusElement.textContent = `MQTT 未连接 (重试: ${reconnectAttempts})`;
        }
    }

    // 更新数据质量指示器（优先显示电气系统的数据质量）
    const qualityElement = document.getElementById('data-quality-indicator');
    if (qualityElement) {
        let successRate = 0;
        let hasData = false;

        // 优先使用电气系统的数据质量
        if (status.electrical && status.electrical.dataProcessing) {
            successRate = status.electrical.dataProcessing.successRate || 0;
            hasData = true;
        } else if (status.cooling && status.cooling.dataProcessing) {
            successRate = status.cooling.dataProcessing.successRate || 0;
            hasData = true;
        }

        if (hasData) {
            qualityElement.textContent = `数据质量: ${successRate.toFixed(1)}%`;

            // 根据成功率设置样式
            qualityElement.className = 'data-quality-indicator';
            if (successRate >= 95) {
                qualityElement.classList.add('excellent');
            } else if (successRate >= 80) {
                qualityElement.classList.add('good');
            } else if (successRate >= 60) {
                qualityElement.classList.add('fair');
            } else {
                qualityElement.classList.add('poor');
            }
        } else {
            qualityElement.textContent = '数据质量: 0%';
            qualityElement.className = 'data-quality-indicator poor';
        }
    }

    // 如果连接状态发生变化，更新电气参数显示
    const overallConnected = status.overall && status.overall.isConnected;
    if (overallConnected !== this.lastConnectionStatus) {
        this.lastConnectionStatus = overallConnected;
        if (!overallConnected) {
            updateMQTTDisconnectedStatus();
        }
    }
}

/**
 * 获取 MQTT 连接状态（供外部调用）
 */
function getMQTTConnectionStatus() {
    const result = {
        electrical: null,
        cooling: null,
        overall: {
            isConnected: false,
            connectedSystems: 0,
            totalSystems: 2
        }
    };

    // 电气系统连接状态
    if (mqttElectricalManager) {
        result.electrical = mqttElectricalManager.getConnectionStatus();
        if (result.electrical.isConnected) {
            result.overall.connectedSystems++;
        }
    } else {
        result.electrical = {
            isConnected: false,
            error: 'MQTT 电气系统管理器未初始化',
            lastDataUpdate: null,
            reconnectAttempts: 0
        };
    }

    // 水冷系统连接状态
    if (mqttCoolingManager) {
        result.cooling = mqttCoolingManager.getConnectionStatus();
        if (result.cooling.isConnected) {
            result.overall.connectedSystems++;
        }
    } else {
        result.cooling = {
            isConnected: false,
            error: 'MQTT 水冷系统管理器未初始化',
            lastDataUpdate: null,
            reconnectAttempts: 0
        };
    }

    // 整体连接状态
    result.overall.isConnected = result.overall.connectedSystems > 0;

    return result;
}

/**
 * 手动重连 MQTT（供外部调用）
 */
async function reconnectMQTT() {
    console.log('手动触发 MQTT 重连...');
    if (mqttElectricalManager) {
        await mqttElectricalManager.reconnect();
    } else {
        console.error('MQTT 电气系统管理器未初始化');
    }
}

/**
 * 手动重连水冷系统 MQTT（供外部调用）
 */
async function reconnectCoolingMQTT() {
    console.log('手动触发水冷系统 MQTT 重连...');
    if (mqttCoolingManager) {
        await mqttCoolingManager.reconnect();
    } else {
        console.error('MQTT 水冷系统管理器未初始化');
    }
}

/**
 * 启用/禁用调试模式
 * @param {boolean} enabled - 是否启用调试模式
 */
function setDebugMode(enabled) {
    debugMode = enabled;
    console.log(`调试模式已${enabled ? '启用' : '禁用'}`);

    if (enabled) {
        console.log('调试模式功能：');
        console.log('- getMQTTDebugInfo() - 获取 MQTT 调试信息');
        console.log('- getConnectionHistory() - 获取连接历史');
        console.log('- getDataHistory() - 获取数据接收历史');
        console.log('- getErrorHistory() - 获取错误历史');
        console.log('- clearDebugHistory() - 清空调试历史');
    }
}

/**
 * 获取 MQTT 调试信息
 */
function getMQTTDebugInfo() {
    const result = {
        electrical: null,
        cooling: null,
        connectionHistory: connectionHistory.slice(-10),
        dataHistory: dataHistory.slice(-5),
        errorHistory: errorHistory.slice(-10)
    };

    // 电气系统调试信息
    if (mqttElectricalManager) {
        const electricalStatus = mqttElectricalManager.getConnectionStatus();
        const electricalStats = mqttElectricalManager.getDataProcessingStatistics();

        result.electrical = {
            connectionStatus: electricalStatus,
            processingStatistics: electricalStats,
            supportedParameters: mqttElectricalManager.getSupportedParameters()
        };
    } else {
        result.electrical = { error: 'MQTT 电气系统管理器未初始化' };
    }

    // 水冷系统调试信息
    if (mqttCoolingManager) {
        const coolingStatus = mqttCoolingManager.getConnectionStatus();
        const coolingStats = mqttCoolingManager.getDataProcessingStatistics();

        result.cooling = {
            connectionStatus: coolingStatus,
            processingStatistics: coolingStats,
            supportedParameters: mqttCoolingManager.getSupportedParameters()
        };
    } else {
        result.cooling = { error: 'MQTT 水冷系统管理器未初始化' };
    }

    return result;
}

/**
 * 记录连接历史
 * @param {string} event - 事件类型
 * @param {string} details - 详细信息
 */
function logConnectionEvent(event, details) {
    const record = {
        timestamp: new Date().toISOString(),
        event: event,
        details: details
    };

    connectionHistory.push(record);

    // 保持历史记录在合理范围内
    if (connectionHistory.length > 50) {
        connectionHistory = connectionHistory.slice(-30);
    }

    if (debugMode) {
        console.log(`连接事件: ${event} - ${details}`);
    }
}

/**
 * 记录数据历史
 * @param {Object} data - 数据信息
 */
function logDataEvent(data) {
    const record = {
        timestamp: new Date().toISOString(),
        parametersCount: data.properties ? Object.keys(data.properties).length : 0,
        dataQuality: data.metadata ? data.metadata.dataQuality.score : 0,
        sample: data.properties ? Object.keys(data.properties).slice(0, 3) : []
    };

    dataHistory.push(record);

    // 保持历史记录在合理范围内
    if (dataHistory.length > 20) {
        dataHistory = dataHistory.slice(-10);
    }

    if (debugMode) {
        console.log(`数据事件: 接收到 ${record.parametersCount} 个参数，质量 ${record.dataQuality.toFixed(1)}%`);
    }
}

/**
 * 记录错误历史
 * @param {string} error - 错误信息
 * @param {string} context - 错误上下文
 */
function logErrorEvent(error, context) {
    const record = {
        timestamp: new Date().toISOString(),
        error: error,
        context: context
    };

    errorHistory.push(record);

    // 保持历史记录在合理范围内
    if (errorHistory.length > 30) {
        errorHistory = errorHistory.slice(-20);
    }

    if (debugMode) {
        console.error(`错误事件: ${error} (${context})`);
    }
}

/**
 * 获取连接历史
 */
function getConnectionHistory() {
    return connectionHistory;
}

/**
 * 获取数据历史
 */
function getDataHistory() {
    return dataHistory;
}

/**
 * 获取错误历史
 */
function getErrorHistory() {
    return errorHistory;
}

/**
 * 清空调试历史
 */
function clearDebugHistory() {
    connectionHistory = [];
    dataHistory = [];
    errorHistory = [];
    console.log('调试历史已清空');
}

/**
 * 初始化弹窗事件监听
 */
function initModalEvents() {
    // 点击弹窗外部区域关闭弹窗
    document.addEventListener('click', function (event) {
        // I/O状态弹窗
        const ioModal = document.getElementById('ioStatusModal');
        if (ioModal && ioModal.style.display === 'flex') {
            if (event.target === ioModal) {
                closeIOStatusModal();
            }
        }

        // 拓扑图弹窗
        const topologyModal = document.getElementById('topologyModal');
        if (topologyModal && topologyModal.style.display === 'flex') {
            if (event.target === topologyModal) {
                closeTopologyModal();
            }
        }

        // 单元状态弹窗
        const unitModal = document.getElementById('unitStatusModal');
        if (unitModal && unitModal.style.display === 'flex') {
            if (event.target === unitModal) {
                closeUnitStatusModal();
            }
        }
    });

    // ESC键关闭弹窗
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
            const ioModal = document.getElementById('ioStatusModal');
            const topologyModal = document.getElementById('topologyModal');
            const unitModal = document.getElementById('unitStatusModal');

            if (ioModal && ioModal.style.display === 'flex') {
                closeIOStatusModal();
            }

            if (topologyModal && topologyModal.style.display === 'flex') {
                closeTopologyModal();
            }

            if (unitModal && unitModal.style.display === 'flex') {
                closeUnitStatusModal();
            }
        }
    });
}

// ==================== 页面初始化 ====================

/**
 * 页面初始化函数
 */
function initializePage() {
    console.log('开始初始化页面...');

    try {
        // 初始化 Unity WebGL 界面切换功能
        initUnityViewSwitcher();

        // 初始化弹窗事件监听
        initModalEvents();

        // 初始化 MQTT 电气系统数据功能
        initMQTTElectricalData();

        // 初始化 MQTT 水冷系统数据功能
        initMQTTCoolingData();

        // 添加 MQTT 连接状态指示器
        addMQTTStatusIndicator();

        // 启动系统数据更新（包含 MQTT 和模拟数据）
        startSystemDataUpdate();

        // 初始化现有的 parameter-item 元素的 tooltip
        initializeExistingParameterTooltips();

        // 启动 tooltip 恢复机制
        startTooltipRecoveryMechanism();

        console.log('页面初始化完成');

        // 显示调试功能说明
        console.log('=== MQTT 双系统调试功能 ===');
        console.log('基础功能:');
        console.log('  getMQTTConnectionStatus() - 获取双系统 MQTT 连接状态');
        console.log('  reconnectMQTT() - 手动重连电气系统 MQTT');
        console.log('  reconnectCoolingMQTT() - 手动重连水冷系统 MQTT');
        console.log('  setDebugMode(true) - 启用调试模式');
        console.log('  getMQTTDebugInfo() - 获取双系统详细调试信息');
        console.log('测试功能:');
        console.log('  runMQTTIntegrationTest() - 运行完整集成测试');
        console.log('  quickConnectionTest() - 快速连接测试');
        console.log('  checkUIElements() - 检查界面元素');
        console.log('Tooltip 调试功能:');
        console.log('  enableTooltipDebug() - 启用 tooltip 调试模式');
        console.log('  disableTooltipDebug() - 禁用 tooltip 调试模式');
        console.log('  getTooltipSystemStatus() - 获取 tooltip 系统状态');
        console.log('  fixAllTooltips() - 手动修复所有 tooltip');
        console.log('================================');

    } catch (error) {
        console.error('页面初始化失败:', error);
        logErrorEvent(error.message, '页面初始化失败');
    }
}

/**
 * 启动系统数据更新（现在由 MQTT 驱动）
 */
function startSystemDataUpdate() {
    console.log('系统数据更新已切换为 MQTT 驱动模式');

    // 不再使用定时器更新模拟数据，改为由 MQTT 消息驱动
    // MQTT 数据接收时会自动触发界面更新

    // 只在没有 MQTT 数据时显示初始状态
    if (!mqttElectricalManager || !mqttElectricalManager.isConnected) {
        console.log('MQTT 未连接，显示初始状态');
        // 可以显示一些默认的初始状态，但不更新数值
        updateInitialElectricalStatus();
    }
}

/**
 * 初始化现有的 parameter-item 元素的 tooltip
 */
function initializeExistingParameterTooltips() {
    console.log('初始化现有的 parameter-item tooltip...');

    const parameterItems = document.querySelectorAll('.parameter-item');
    let initializedCount = 0;

    parameterItems.forEach(item => {
        // 检查是否已经初始化过
        if (item.getAttribute('data-tooltip-initialized') === 'true') {
            return;
        }

        // 清理可能存在的旧 tooltip
        const existingTooltip = item.querySelector('.parameter-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 创建新的 tooltip 元素
        const tooltip = document.createElement('div');
        tooltip.className = 'parameter-tooltip';
        item.appendChild(tooltip);

        // 标记已初始化
        item.setAttribute('data-tooltip-initialized', 'true');

        // 初始化 tooltip 行为
        initializeParameterTooltipBehavior(item, tooltip);

        // 设置默认内容
        const labelElement = item.querySelector('.parameter-label');
        const valueElement = item.querySelector('.parameter-value');
        if (labelElement && valueElement) {
            const defaultText = `${labelElement.textContent} ${valueElement.textContent}`;
            tooltip.textContent = defaultText;
        }

        initializedCount++;
    });

    console.log(`已初始化 ${initializedCount} 个 parameter-item tooltip`);
}

/**
 * 启用 tooltip 调试模式
 */
function enableTooltipDebug() {
    window.tooltipDebug = true;
    tooltipDebugMode = true;
    console.log('Tooltip 调试模式已启用');
    console.log('点击任意 parameter-item 查看其 tooltip 状态');

    // 启动恢复机制
    startTooltipRecoveryMechanism();
}

/**
 * 禁用 tooltip 调试模式
 */
function disableTooltipDebug() {
    window.tooltipDebug = false;
    tooltipDebugMode = false;
    console.log('Tooltip 调试模式已禁用');

    // 停止恢复机制
    if (tooltipRecoveryInterval) {
        clearInterval(tooltipRecoveryInterval);
        tooltipRecoveryInterval = null;
    }
}

/**
 * 启动 tooltip 恢复机制
 */
function startTooltipRecoveryMechanism() {
    if (tooltipRecoveryInterval) {
        clearInterval(tooltipRecoveryInterval);
    }

    // 每30秒检查一次 tooltip 状态
    tooltipRecoveryInterval = setInterval(() => {
        const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
        let recoveredCount = 0;

        parameterItems.forEach(item => {
            const tooltip = item.querySelector('.parameter-tooltip');
            if (tooltip) {
                const hasShowClass = tooltip.classList.contains('show');
                const isVisible = tooltip.style.visibility === 'visible';
                const opacity = tooltip.style.opacity;

                // 检测异常状态：有 show 类但不可见，或者透明度异常
                if ((hasShowClass && tooltip.style.visibility === 'hidden') ||
                    (hasShowClass && opacity === '0' && isVisible)) {

                    console.warn('检测到异常的 tooltip 状态，正在恢复...', {
                        element: item,
                        hasShowClass,
                        visibility: tooltip.style.visibility,
                        opacity: opacity
                    });

                    // 强制重置状态
                    tooltip.classList.remove('show');
                    tooltip.style.visibility = 'hidden';
                    tooltip.style.opacity = '0';
                    recoveredCount++;
                }
            }
        });

        if (recoveredCount > 0) {
            console.log(`恢复了 ${recoveredCount} 个异常的 tooltip`);
        }
    }, 30000);
}

/**
 * 手动修复所有 tooltip
 */
function fixAllTooltips() {
    const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
    let fixedCount = 0;

    parameterItems.forEach(item => {
        const tooltip = item.querySelector('.parameter-tooltip');
        if (tooltip) {
            // 强制重置所有状态
            tooltip.classList.remove('show');
            tooltip.style.visibility = 'hidden';
            tooltip.style.opacity = '0';
            tooltip.style.left = '';
            tooltip.style.top = '';
            fixedCount++;
        }
    });

    console.log(`已修复 ${fixedCount} 个 tooltip 的状态`);
    return fixedCount;
}

/**
 * 获取 tooltip 系统状态
 */
function getTooltipSystemStatus() {
    const parameterItems = document.querySelectorAll('.parameter-item[data-tooltip-initialized="true"]');
    const status = {
        totalItems: parameterItems.length,
        itemsWithTooltip: 0,
        visibleTooltips: 0,
        abnormalStates: 0,
        details: []
    };

    parameterItems.forEach((item, index) => {
        const tooltip = item.querySelector('.parameter-tooltip');
        if (tooltip) {
            status.itemsWithTooltip++;

            const hasShowClass = tooltip.classList.contains('show');
            const isVisible = tooltip.style.visibility === 'visible';
            const opacity = tooltip.style.opacity;

            if (hasShowClass && isVisible) {
                status.visibleTooltips++;
            }

            // 检测异常状态
            const isAbnormal = (hasShowClass && tooltip.style.visibility === 'hidden') ||
                (hasShowClass && opacity === '0' && isVisible);

            if (isAbnormal) {
                status.abnormalStates++;
            }

            status.details.push({
                index,
                hasTooltip: true,
                hasShowClass,
                visibility: tooltip.style.visibility,
                opacity,
                isAbnormal
            });
        } else {
            status.details.push({
                index,
                hasTooltip: false
            });
        }
    });

    return status;
}

/**
 * 通用的 tooltip 行为初始化函数
 * @param {HTMLElement} parameterItem - parameter-item 元素
 * @param {HTMLElement} tooltip - tooltip 元素
 */
function initializeParameterTooltipBehavior(parameterItem, tooltip) {
    // 状态管理对象，确保状态同步
    const state = {
        showTimeout: null,
        hideTimeout: null,
        animationFrame: null,
        isVisible: false,
        isShowing: false,
        isHiding: false
    };

    // 重置所有状态的函数
    const resetState = () => {
        if (state.showTimeout) {
            clearTimeout(state.showTimeout);
            state.showTimeout = null;
        }
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
        }
        if (state.animationFrame) {
            cancelAnimationFrame(state.animationFrame);
            state.animationFrame = null;
        }
        state.isShowing = false;
        state.isHiding = false;
    };

    // 强制同步状态的函数
    const syncState = () => {
        const hasShowClass = tooltip.classList.contains('show');
        const isVisuallyVisible = tooltip.style.visibility === 'visible' && hasShowClass;

        // 如果状态不一致，强制同步
        if (state.isVisible !== isVisuallyVisible) {
            console.warn('Tooltip state mismatch detected, syncing...', {
                stateVisible: state.isVisible,
                visuallyVisible: isVisuallyVisible,
                hasShowClass: hasShowClass
            });

            state.isVisible = isVisuallyVisible;
            if (!isVisuallyVisible) {
                tooltip.classList.remove('show');
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
            }
        }
    };

    // 显示 tooltip 的函数
    const showTooltip = () => {
        // 清理隐藏相关的定时器
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
        }

        // 同步状态检查
        syncState();

        // 如果已经可见或正在显示，直接返回
        if (state.isVisible || state.isShowing) return;

        state.isShowing = true;
        state.isHiding = false;

        // 先显示 tooltip 以获取正确的尺寸
        tooltip.style.opacity = '0';
        tooltip.style.visibility = 'visible';

        // 使用 requestAnimationFrame 确保 DOM 更新完成
        state.animationFrame = requestAnimationFrame(() => {
            // 检查是否在动画执行前被取消
            if (!state.isShowing) {
                tooltip.style.visibility = 'hidden';
                return;
            }

            try {
                const rect = parameterItem.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();

                // 计算 tooltip 位置，确保不超出视窗边界
                let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
                let top = rect.top - tooltipRect.height - 10;

                // 边界检查
                if (left < 10) left = 10;
                if (left + tooltipRect.width > window.innerWidth - 10) {
                    left = window.innerWidth - tooltipRect.width - 10;
                }
                if (top < 10) {
                    top = rect.bottom + 10; // 如果上方空间不足，显示在下方
                }

                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';

                // 再次检查是否仍然需要显示
                if (state.isShowing) {
                    tooltip.classList.add('show');
                    state.isVisible = true;
                }
            } catch (error) {
                console.warn('Tooltip positioning error:', error);
                tooltip.style.visibility = 'hidden';
            }

            state.isShowing = false;
            state.animationFrame = null;
        });
    };

    // 隐藏 tooltip 的函数
    const hideTooltip = () => {
        // 清理显示相关的定时器和动画
        if (state.showTimeout) {
            clearTimeout(state.showTimeout);
            state.showTimeout = null;
        }
        if (state.animationFrame) {
            cancelAnimationFrame(state.animationFrame);
            state.animationFrame = null;
        }

        state.isShowing = false;

        // 同步状态检查
        syncState();

        if (!state.isVisible || state.isHiding) return;

        state.isHiding = true;
        tooltip.classList.remove('show');
        state.isVisible = false;

        // 延迟隐藏，等待动画完成
        state.hideTimeout = setTimeout(() => {
            if (state.isHiding) {  // 确保没有被新的显示操作打断
                tooltip.style.visibility = 'hidden';
                tooltip.style.opacity = '0';
                state.isHiding = false;
            }
        }, 300);
    };

    // 绑定鼠标事件（只绑定一次）
    parameterItem.addEventListener('mouseenter', () => {
        resetState();
        state.showTimeout = setTimeout(showTooltip, 200); // 200ms 延迟显示
    });

    parameterItem.addEventListener('mouseleave', () => {
        resetState();
        hideTooltip();
    });

    // 防止快速移动鼠标时的闪烁
    tooltip.addEventListener('mouseenter', () => {
        if (state.hideTimeout) {
            clearTimeout(state.hideTimeout);
            state.hideTimeout = null;
            state.isHiding = false;
        }
    });

    tooltip.addEventListener('mouseleave', () => {
        hideTooltip();
    });

    // 添加调试功能（可选）
    if (window.tooltipDebug) {
        parameterItem.addEventListener('click', () => {
            console.log('Tooltip state:', {
                ...state,
                hasShowClass: tooltip.classList.contains('show'),
                visibility: tooltip.style.visibility,
                opacity: tooltip.style.opacity
            });
        });
    }
}

/**
 * 更新初始电气系统状态（仅状态指示器，不包含数值）
 */
function updateInitialElectricalStatus() {
    console.log('设置电气系统初始状态');

    // 设置所有状态为未激活状态
    const statusElements = ['ready-status', 'fault-status', 'running-status', 'standby-status', 'hv-wait-status'];
    statusElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.classList.remove('active');
            element.classList.add('inactive');
        }
    });

    // 清空数值显示，显示等待数据状态，并设置为红色
    const valueElements = [
        'load-reactive-power-value',
        'power-factor-value',
        'grid-reactive-current-value',
        'bus-voltage-uab-value',
        'bus-voltage-ubc-value',
        'bus-voltage-uca-value',
        'svg-current-ia-value',
        'svg-current-ib-value',
        'svg-current-ic-value'
    ];

    valueElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '等待数据...';
            element.style.color = '#ff4444'; // 设置为红色警告颜色
        }
    });
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    // 如果页面已经加载完成，直接初始化
    initializePage();
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function () {
    if (mqttElectricalManager) {
        logConnectionEvent('page_unload', '页面卸载，断开 MQTT 连接');
        mqttElectricalManager.disconnect();
    }

    // 清理 tooltip 恢复机制
    if (tooltipRecoveryInterval) {
        clearInterval(tooltipRecoveryInterval);
        tooltipRecoveryInterval = null;
    }

    console.log('页面卸载，资源已清理');
});
