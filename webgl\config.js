/**
 * 桂林智源 SVG 数字化系统 - 配置文件
 * 统一管理API认证token和其他全局配置
 */

/**
 * 通用Cookie获取函数
 * @param {string} name - Cookie名称
 * @returns {string|null} Cookie值或null
 */
function getCookie(name) {
    if (typeof document === 'undefined') {
        return null;
    }

    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
}

/**
 * 通用Cookie删除函数
 * @param {string} name - Cookie名称
 */
function deleteCookie(name) {
    if (typeof document !== 'undefined') {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/`;
    }
}

/**
 * 验证Token有效性
 * @param {string} token - 要验证的token
 * @returns {Promise<boolean>} token是否有效
 */
async function validateToken(token) {
    if (!token || token.length < 10) {
        return false;
    }

    // 检查Token格式（JWT通常以eyJ开头）
    if (!token.startsWith('eyJ')) {
        return false;
    }

    // 通过调用一个需要认证的API来验证Token是否有效
    if (typeof getAlertDeviceList === 'function') {
        // 临时设置Token到Cookie中进行验证
        const originalToken = getCookie('Admin-Token');
        updateAuthToken(token);

        try {
            const result = await getAlertDeviceList({ pageSize: 1 });

            // 恢复原始Token
            if (originalToken) {
                updateAuthToken(originalToken);
            } else {
                deleteCookie('Admin-Token');
            }

            return result.success;
        } catch (error) {
            // 恢复原始Token
            if (originalToken) {
                updateAuthToken(originalToken);
            } else {
                deleteCookie('Admin-Token');
            }
            return false;
        }
    } else {
        // 如果没有API验证函数，只进行基本格式检查
        return token.length > 50;
    }
}

/**
 * 从Cookies中获取认证Token
 * @returns {string|null} 认证Token或null
 */
function getTokenFromCookies() {
    console.log('[Token获取] 开始从Cookies获取认证Token...');

    // 检查是否在浏览器环境中
    if (typeof document === 'undefined') {
        console.warn('[Token获取] 非浏览器环境，无法访问document对象');
        return null;
    }

    console.log('[Token获取] 当前document.cookie内容:', document.cookie);

    // 从Cookies中获取Admin-Token
    const cookies = document.cookie.split(';');
    console.log('[Token获取] 解析到的cookie总数:', cookies.length);

    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        console.log(`[Token获取] 检查第${i + 1}个cookie: "${cookie}"`);

        const [name, value] = cookie.split('=');
        console.log(`[Token获取] cookie名称: "${name}", 值长度: ${value ? value.length : 0}`);

        if (name === 'Admin-Token') {
            const decodedValue = decodeURIComponent(value);
            console.log('[Token获取] ✅ 成功找到Admin-Token:', decodedValue.substring(0, 20) + '...');
            return decodedValue;
        }
    }

    console.warn('[Token获取] ❌ 未找到Admin-Token，所有cookie名称:',
        cookies.map(c => c.trim().split('=')[0]));
    return null;
}

// 统一的JWT Token配置
const CONFIG = {
    // API基础URL
    BASE_URL: 'http://*************/prod-api',

    // 请求超时时间（毫秒）
    REQUEST_TIMEOUT: 10000,

    // 重试次数
    MAX_RETRIES: 3,

    // 备用Token（当无法从Cookies获取时使用）
    FALLBACK_TOKEN: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjVmM2IwZTZmLWJmMDMtNGViYy05YjdlLTMwNjA4Y2FhZWNmOCJ9.1O79OeRWpHjRa5nps9L9ETtplMTLmUiiZpmLi-waRkGnM4M72fgWdj4QGxqx_-ccCedyixw5jpfeWtniY5RHXQ',

    // 动态获取JWT认证Token的getter
    get AUTH_TOKEN() {
        const cookieToken = getTokenFromCookies();
        if (cookieToken) {
            return cookieToken;
        }
        // 如果无法从Cookies获取，使用备用Token
        console.warn('无法从Cookies获取认证Token，使用备用Token');
        return this.FALLBACK_TOKEN;
    }
};

/**
 * 获取认证头信息
 * @returns {Object} 包含Authorization头的对象
 */
function getAuthHeader() {
    return {
        'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`
    };
}
/**
 * 获取严格认证头信息（仅从Cookie读取，不使用备用Token）
 * @returns {Object} 包含Authorization头的对象
 * @throws {Error} 当未获取到Cookie中的Admin-Token时抛出错误
 */
function getStrictAuthHeader() {
    const cookieToken = getTokenFromCookies();
    if (!cookieToken) {
        throw new Error('认证失败：未获取到Admin-Token，请先登录');
    }
    return { 'Authorization': `Bearer ${cookieToken}` };
}


/**
 * 获取完整的请求配置
 * @param {Object} additionalHeaders 额外的请求头
 * @returns {Object} 完整的请求配置
 */
function getRequestConfig(additionalHeaders = {}) {
    return {
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...getAuthHeader(),
            ...additionalHeaders
        },
        timeout: CONFIG.REQUEST_TIMEOUT
    };
}

/**
 * 更新认证Token到Cookies
 * @param {string} newToken 新的JWT Token
 */
/**
 * 更新认证Token到Cookies（增强调试版本）
 * - 优先写入与当前页面同源的Host-Only Cookie，确保JS可读
 * - 如当前域属于 qizhiyun.cc，则额外写入 domain=.qizhiyun.cc 以兼容生产
 * - 在HTTPS下自动添加 SameSite=None; Secure
 * @param {string} newToken 新的JWT Token
 */
function updateAuthToken(newToken) {
    console.log('[认证Token] 开始更新认证Token到Cookies...');
    console.log('[认证Token] 新Token长度:', newToken ? newToken.length : 0);
    console.log('[认证Token] 新Token前20字符:', newToken ? newToken.substring(0, 20) + '...' : 'null');

    if (typeof document !== 'undefined') {
        try {
            // 获取当前环境信息
            const currentUrl = typeof location !== 'undefined' ? location.href : 'unknown';
            const currentHostname = typeof location !== 'undefined' ? location.hostname : 'unknown';
            const currentProtocol = typeof location !== 'undefined' ? location.protocol : 'unknown';
            const isHttps = currentProtocol === 'https:';

            console.log('[认证Token] 当前环境信息:');
            console.log('  - URL:', currentUrl);
            console.log('  - Hostname:', currentHostname);
            console.log('  - Protocol:', currentProtocol);
            console.log('  - Is HTTPS:', isHttps);

            // 记录写入前的Cookie状态
            console.log('[认证Token] 写入前的document.cookie:', document.cookie);

            // 构建Cookie属性
            const attrs = ['path=/'];
            if (isHttps) {
                // 跨站场景下允许第三方上下文携带Cookie
                attrs.push('SameSite=None', 'Secure');
                console.log('[认证Token] HTTPS环境，添加SameSite=None和Secure属性');
            } else {
                // HTTP环境下使用更宽松的SameSite策略
                attrs.push('SameSite=Lax');
                console.log('[认证Token] HTTP环境，添加SameSite=Lax属性');
            }

            // 1) 写入Host-Only Cookie（无domain，前端可读）
            const hostOnlyCookie = `Admin-Token=${encodeURIComponent(newToken)}; ${attrs.join('; ')}`;
            console.log('[认证Token] 准备写入Host-Only Cookie:', hostOnlyCookie);
            document.cookie = hostOnlyCookie;
            console.log('[认证Token] Host-Only Cookie已写入');

            // 2) 如域名属于qizhiyun.cc，再写入带domain的Cookie以兼容生产
            if (typeof location !== 'undefined' && /(^|\.)qizhiyun\.cc$/.test(location.hostname)) {
                const domainCookie = `Admin-Token=${encodeURIComponent(newToken)}; ${attrs.join('; ')}; domain=.qizhiyun.cc`;
                console.log('[认证Token] 准备写入Domain Cookie:', domainCookie);
                document.cookie = domainCookie;
                console.log('[认证Token] Domain Cookie已写入');
            }

            // 记录写入后的Cookie状态
            console.log('[认证Token] 写入后的document.cookie:', document.cookie);

            // 3) 验证是否可读；若不可读，提示并保留Host-Only版本
            console.log('[认证Token] 开始验证Cookie读取...');
            const verify = getTokenFromCookies();
            if (verify) {
                console.log('[认证Token] ✅ Cookie验证成功，可正常读取');
                console.log('[认证Token] 验证读取的Token前20字符:', verify.substring(0, 20) + '...');
                console.log('[认证Token] Token匹配:', verify === newToken ? '✅ 完全匹配' : '❌ 不匹配');
            } else {
                console.error('[认证Token] ❌ Cookie验证失败，无法读取Admin-Token');
                console.error('[认证Token] 这可能导致后续API调用失败');

                // 尝试不同的Cookie写入策略
                console.log('[认证Token] 尝试备用Cookie写入策略...');

                // 策略1: 不使用encodeURIComponent
                document.cookie = `Admin-Token=${newToken}; path=/`;
                console.log('[认证Token] 备用策略1: 不编码直接写入');

                // 再次验证
                const verify2 = getTokenFromCookies();
                if (verify2) {
                    console.log('[认证Token] ✅ 备用策略1成功');
                } else {
                    console.log('[认证Token] ❌ 备用策略1失败');

                    // 策略2: 使用更简单的属性
                    document.cookie = `Admin-Token=${encodeURIComponent(newToken)}`;
                    console.log('[认证Token] 备用策略2: 最简属性写入');

                    const verify3 = getTokenFromCookies();
                    if (verify3) {
                        console.log('[认证Token] ✅ 备用策略2成功');
                    } else {
                        console.error('[认证Token] ❌ 所有Cookie写入策略都失败');
                    }
                }
            }

            console.log('[认证Token] ✅ 认证Token更新流程完成');
        } catch (e) {
            console.error('[认证Token] ❌ 写入Cookie失败:', e);
            console.error('[认证Token] 错误详情:', e.stack);
        }
    } else {
        // 非浏览器环境，更新备用Token
        CONFIG.FALLBACK_TOKEN = newToken;
        console.log('[认证Token] 非浏览器环境，已更新备用Token');
    }
}

/**
 * 获取当前Token（调试用）
 * @returns {string} 当前Token
 */
function getCurrentToken() {
    return CONFIG.AUTH_TOKEN;
}

/**
 * 强制刷新Token（从Cookies重新获取）
 * @returns {string} 刷新后的Token
 */
function refreshToken() {
    const token = CONFIG.AUTH_TOKEN; // 触发getter重新获取
    console.log('Token已刷新:', token ? '已获取' : '未获取到');
    return token;
}

/**
 * Cookie认证调试函数 - 全面诊断Cookie问题
 * @returns {Object} 详细的调试信息
 */
function debugCookieAuth() {
    console.log('🔍 开始Cookie认证调试...');

    const debugInfo = {
        timestamp: new Date().toISOString(),
        environment: {},
        cookies: {},
        token: {},
        recommendations: []
    };

    // 1. 环境信息
    if (typeof window !== 'undefined') {
        debugInfo.environment = {
            userAgent: navigator.userAgent,
            url: location.href,
            hostname: location.hostname,
            protocol: location.protocol,
            port: location.port,
            pathname: location.pathname,
            isLocalhost: location.hostname === 'localhost' || location.hostname === '127.0.0.1',
            isHttps: location.protocol === 'https:'
        };
    }

    // 2. Cookie信息
    if (typeof document !== 'undefined') {
        debugInfo.cookies = {
            raw: document.cookie,
            count: document.cookie ? document.cookie.split(';').length : 0,
            parsed: {},
            hasAdminToken: false
        };

        // 解析所有Cookie
        if (document.cookie) {
            const cookies = document.cookie.split(';');
            cookies.forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                debugInfo.cookies.parsed[name] = value;
                if (name === 'Admin-Token') {
                    debugInfo.cookies.hasAdminToken = true;
                }
            });
        }
    }

    // 3. Token信息
    const cookieToken = getTokenFromCookies();
    const configToken = CONFIG.AUTH_TOKEN;
    const fallbackToken = CONFIG.FALLBACK_TOKEN;

    debugInfo.token = {
        fromCookie: cookieToken ? {
            exists: true,
            length: cookieToken.length,
            preview: cookieToken.substring(0, 20) + '...',
            isValid: cookieToken.length > 50 // 简单验证
        } : { exists: false },
        fromConfig: configToken ? {
            exists: true,
            length: configToken.length,
            preview: configToken.substring(0, 20) + '...',
            isValid: configToken.length > 50
        } : { exists: false },
        fallback: fallbackToken ? {
            exists: true,
            length: fallbackToken.length,
            preview: fallbackToken.substring(0, 20) + '...'
        } : { exists: false },
        cookieMatchesConfig: cookieToken === configToken,
        usingFallback: !cookieToken && configToken === fallbackToken
    };

    // 4. 生成建议
    if (!debugInfo.cookies.hasAdminToken) {
        debugInfo.recommendations.push('❌ 未找到Admin-Token Cookie，请先执行登录');
    }

    if (debugInfo.environment.isLocalhost && !debugInfo.environment.isHttps) {
        debugInfo.recommendations.push('⚠️ 本地HTTP环境，Cookie可能受到浏览器安全策略限制');
    }

    if (debugInfo.token.usingFallback) {
        debugInfo.recommendations.push('⚠️ 正在使用备用Token，可能导致认证问题');
    }

    if (!debugInfo.token.cookieMatchesConfig) {
        debugInfo.recommendations.push('⚠️ Cookie中的Token与配置中的Token不匹配');
    }

    if (debugInfo.recommendations.length === 0) {
        debugInfo.recommendations.push('✅ Cookie认证配置看起来正常');
    }

    console.log('🔍 Cookie认证调试完成:', debugInfo);
    return debugInfo;
}

// 全局导出，供其他文件使用
if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
    window.getAuthHeader = getAuthHeader;
    window.getStrictAuthHeader = getStrictAuthHeader;
    window.getRequestConfig = getRequestConfig;
    window.updateAuthToken = updateAuthToken;
    window.getCurrentToken = getCurrentToken;
    window.refreshToken = refreshToken;
    window.getTokenFromCookies = getTokenFromCookies;
    window.debugCookieAuth = debugCookieAuth;
}

/**
 * ===========================================
 * API接口功能实现模块
 * ===========================================
 */

/**
 * 网络配置参数 - 可根据实际环境修改
 */
const NETWORK_CONFIG = {
    // 服务器IP地址
    SERVER_IP: '*************',

    // 服务器端口
    SERVER_PORT: 80,

    // API基础路径
    API_BASE_PATH: '/prod-api',

    // 完整的API基础URL
    get API_BASE_URL() {
        return `http://${this.SERVER_IP}${this.SERVER_PORT === 80 ? '' : ':' + this.SERVER_PORT}${this.API_BASE_PATH}`;
    }
};

/**
 * API端点配置
 */
const API_ENDPOINTS = {
    // 登录接口
    LOGIN: '/login',

    // 设备告警日志列表
    ALERT_LOG_LIST: '/iot/alertLog/list',

    // 设备运行状态信息
    DEVICE_RUNNING_STATUS: '/iot/device/runningStatus',

    // 定值接口（待开发）
    DEVICE_SETTINGS: '/iot/device/settings'
};

/**
 * 通用HTTP请求函数
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 响应数据
 */
async function makeHttpRequest(url, options = {}) {
    const {
        method = 'GET',
        headers = {},
        body = null,
        timeout = CONFIG.REQUEST_TIMEOUT,
        requireAuth = true,
        strictAuth = true
    } = options;

    console.log(`[HTTP请求] ${method} ${url}`);

    // 构建请求头
    const requestHeaders = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...headers
    };

    // 如果需要认证，添加Authorization头
    if (requireAuth) {
        try {
            const authHeader = strictAuth ? getStrictAuthHeader() : getAuthHeader();
            Object.assign(requestHeaders, authHeader);
            console.log('[HTTP请求] 已添加认证头');
        } catch (authError) {
            console.error('[HTTP请求] 认证失败:', authError.message);
            throw authError;
        }
    }

    // 构建请求配置
    const requestConfig = {
        method,
        headers: requestHeaders,
        ...(body && { body: typeof body === 'string' ? body : JSON.stringify(body) })
    };

    try {
        // 创建超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        requestConfig.signal = controller.signal;

        console.log('[HTTP请求] 发送请求...');
        const response = await fetch(url, requestConfig);
        clearTimeout(timeoutId);

        console.log(`[HTTP请求] 响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
                throw new Error(`认证失败或已过期（HTTP ${response.status}），请重新登录`);
            }
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('[HTTP请求] 响应数据:', data);
        return data;

    } catch (error) {
        console.error('[HTTP请求] 请求失败:', error);
        throw error;
    }
}

/**
 * 用户登录接口
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @param {string} uuid - UUID（可选）
 * @param {number} sourceType - 来源类型，默认为1
 * @returns {Promise<Object>} 登录响应数据
 */
async function loginUser(username = 'bydq_admin', password = 'Aa123456', uuid = '', sourceType = 1) {
    console.log('[用户登录] 开始登录流程...');

    const loginData = {
        username,
        password,
        uuid,
        sourceType
    };

    const url = `${NETWORK_CONFIG.API_BASE_URL}${API_ENDPOINTS.LOGIN}`;

    try {
        const response = await makeHttpRequest(url, {
            method: 'POST',
            body: loginData,
            requireAuth: false // 登录接口不需要认证
        });

        // 检查登录是否成功
        if (response.code === 200 && response.token) {
            console.log('[用户登录] ✅ 登录成功');

            // 更新认证Token到Cookies
            updateAuthToken(response.token);

            return {
                success: true,
                message: response.msg || '登录成功',
                token: response.token,
                data: response
            };
        } else {
            console.warn('[用户登录] ❌ 登录失败:', response.msg);
            return {
                success: false,
                message: response.msg || '登录失败',
                data: response
            };
        }

    } catch (error) {
        console.error('[用户登录] 登录异常:', error);
        return {
            success: false,
            message: `登录异常: ${error.message}`,
            error: error
        };
    }
}

/**
 * 获取报警设备列表
 * @param {Object} params - 查询参数
 * @param {string} params.alertName - 报警名称，默认为'报警设备'
 * @param {number} params.pageNum - 页码，默认为1
 * @param {number} params.pageSize - 每页大小，默认为10
 * @returns {Promise<Object>} 报警列表响应数据
 */
async function getAlertDeviceList(params = {}) {
    console.log('[报警列表] 开始获取报警设备列表...');

    const {
        alertName = '报警设备',
        pageNum = 1,
        pageSize = 10,
        ...otherParams
    } = params;

    // 构建查询参数
    const queryParams = new URLSearchParams({
        alertName: alertName,
        pageNum: pageNum.toString(),
        pageSize: pageSize.toString(),
        ...otherParams
    });

    const url = `${NETWORK_CONFIG.API_BASE_URL}${API_ENDPOINTS.ALERT_LOG_LIST}?${queryParams}`;

    try {
        const response = await makeHttpRequest(url, {
            method: 'GET'
        });

        if (response.code === 200) {
            console.log(`[报警列表] ✅ 成功获取报警列表，共${response.total}条记录`);
            return {
                success: true,
                message: response.msg || '查询成功',
                total: response.total || 0,
                data: response.rows || [],
                rawResponse: response
            };
        } else {
            console.warn('[报警列表] ❌ 获取报警列表失败:', response.msg);
            return {
                success: false,
                message: response.msg || '查询失败',
                data: [],
                rawResponse: response
            };
        }

    } catch (error) {
        console.error('[报警列表] 获取报警列表异常:', error);
        return {
            success: false,
            message: `查询异常: ${error.message}`,
            data: [],
            error: error
        };
    }
}

/**
 * 获取故障设备列表
 * @param {Object} params - 查询参数
 * @param {string} params.alertName - 报警名称，默认为'故障设备'
 * @param {number} params.pageNum - 页码，默认为1
 * @param {number} params.pageSize - 每页大小，默认为10
 * @returns {Promise<Object>} 故障列表响应数据
 */
async function getFaultDeviceList(params = {}) {
    console.log('[故障列表] 开始获取故障设备列表...');

    const {
        alertName = '故障设备',
        pageNum = 1,
        pageSize = 10,
        ...otherParams
    } = params;

    // 构建查询参数
    const queryParams = new URLSearchParams({
        alertName: alertName,
        pageNum: pageNum.toString(),
        pageSize: pageSize.toString(),
        ...otherParams
    });

    const url = `${NETWORK_CONFIG.API_BASE_URL}${API_ENDPOINTS.ALERT_LOG_LIST}?${queryParams}`;

    try {
        const response = await makeHttpRequest(url, {
            method: 'GET'
        });

        if (response.code === 200) {
            console.log(`[故障列表] ✅ 成功获取故障列表，共${response.total}条记录`);
            return {
                success: true,
                message: response.msg || '查询成功',
                total: response.total || 0,
                data: response.rows || [],
                rawResponse: response
            };
        } else {
            console.warn('[故障列表] ❌ 获取故障列表失败:', response.msg);
            return {
                success: false,
                message: response.msg || '查询失败',
                data: [],
                rawResponse: response
            };
        }

    } catch (error) {
        console.error('[故障列表] 获取故障列表异常:', error);
        return {
            success: false,
            message: `查询异常: ${error.message}`,
            data: [],
            error: error
        };
    }
}

/**
 * 获取实时报警日志列表（通用函数）
 * 支持获取所有类型的报警日志，包括报警设备、故障设备等
 * @param {Object} params - 查询参数
 * @param {string} params.alertName - 报警名称，可选值：'报警设备'、'故障设备'等，不传则获取所有类型
 * @param {number} params.pageNum - 页码，默认为1
 * @param {number} params.pageSize - 每页大小，默认为100
 * @param {string} params.beginTime - 开始时间，格式：YYYY-MM-DD HH:mm:ss
 * @param {string} params.endTime - 结束时间，格式：YYYY-MM-DD HH:mm:ss
 * @returns {Promise<Object>} 报警日志列表响应数据
 */
async function getRealTimeAlarmLogList(params = {}) {
    console.log('[实时报警] 开始获取实时报警日志列表...');

    const {
        alertName,
        pageNum = 1,
        pageSize = 100,
        beginTime,
        endTime,
        ...otherParams
    } = params;

    // 构建查询参数
    const queryParams = new URLSearchParams({
        pageNum: pageNum.toString(),
        pageSize: pageSize.toString(),
        ...otherParams
    });

    // 只有当alertName有值时才添加到查询参数中
    if (alertName) {
        queryParams.append('alertName', alertName);
    }

    // 添加时间范围参数
    if (beginTime) {
        queryParams.append('beginTime', beginTime);
    }
    if (endTime) {
        queryParams.append('endTime', endTime);
    }

    const url = `${NETWORK_CONFIG.API_BASE_URL}${API_ENDPOINTS.ALERT_LOG_LIST}?${queryParams}`;

    try {
        const response = await makeHttpRequest(url, {
            method: 'GET'
        });

        if (response.code === 200) {
            console.log(`[实时报警] ✅ 成功获取报警日志列表，共${response.total}条记录`);

            // 处理数据，过滤和格式化
            const processedData = processAlarmLogData(response.rows || []);

            return {
                success: true,
                message: response.msg || '查询成功',
                total: response.total || 0,
                data: processedData,
                rawData: response.rows || [],
                rawResponse: response
            };
        } else {
            console.warn('[实时报警] ❌ 获取报警日志列表失败:', response.msg);
            return {
                success: false,
                message: response.msg || '查询失败',
                data: [],
                rawResponse: response
            };
        }

    } catch (error) {
        console.error('[实时报警] 获取报警日志列表异常:', error);
        return {
            success: false,
            message: `查询异常: ${error.message}`,
            data: [],
            error: error
        };
    }
}

/**
 * 处理报警日志数据
 * 解析detail字段中的JSON数据，过滤和格式化事件信息
 * @param {Array} rawData - 原始数据数组
 * @returns {Array} 处理后的数据数组
 */
function processAlarmLogData(rawData) {
    const processedEvents = [];

    rawData.forEach((item) => {
        try {
            // 只处理报警设备和故障设备，过滤掉其他类型
            if (item.alertName !== '报警设备' && item.alertName !== '故障设备') {
                return;
            }

            // 解析detail字段中的JSON数据，获取事件名称
            let eventName = '';
            if (item.detail) {
                try {
                    const detailObj = JSON.parse(item.detail);
                    eventName = detailObj.name || '';
                } catch (e) {
                    console.warn('解析detail字段失败:', e);
                    eventName = item.detail;
                }
            }

            // 创建事件对象
            const event = {
                id: item.alertLogId,
                alertName: item.alertName,
                eventName: eventName,
                createTime: item.createTime,
                serialNumber: item.serialNumber,
                deviceName: item.deviceName,
                alertLevel: item.alertLevel,
                status: item.status,
                detail: item.detail,
                originalData: item
            };

            processedEvents.push(event);
        } catch (error) {
            console.error('处理事件数据失败:', error, item);
        }
    });

    // 按创建时间倒序排列（最新的在前面）
    processedEvents.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

    console.log(`[实时报警] 处理完成，有效事件数量: ${processedEvents.length}`);
    return processedEvents;
}

/**
 * 获取设备运行状态信息
 * @param {number} deviceId - 设备ID，默认为298
 * @returns {Promise<Object>} 设备状态响应数据
 */
async function getDeviceRunningStatus(deviceId = 298) {
    console.log(`[设备状态] 开始获取设备${deviceId}的运行状态...`);

    const queryParams = new URLSearchParams({
        deviceId: deviceId.toString()
    });

    const url = `${NETWORK_CONFIG.API_BASE_URL}${API_ENDPOINTS.DEVICE_RUNNING_STATUS}?${queryParams}`;

    try {
        const response = await makeHttpRequest(url, {
            method: 'GET'
        });

        if (response.code === 200 && response.data) {
            console.log('[设备状态] ✅ 成功获取设备运行状态');

            // 解析thingsModelValue JSON字符串
            let parsedThingsModelValue = [];
            if (response.data.thingsModelValue) {
                try {
                    parsedThingsModelValue = JSON.parse(response.data.thingsModelValue);
                } catch (parseError) {
                    console.warn('[设备状态] thingsModelValue解析失败:', parseError);
                }
            }

            return {
                success: true,
                message: response.msg || '查询成功',
                data: {
                    ...response.data,
                    parsedThingsModelValue // 添加解析后的数据
                },
                rawResponse: response
            };
        } else {
            console.warn('[设备状态] ❌ 获取设备状态失败:', response.msg);
            return {
                success: false,
                message: response.msg || '查询失败',
                data: null,
                rawResponse: response
            };
        }

    } catch (error) {
        console.error('[设备状态] 获取设备状态异常:', error);
        return {
            success: false,
            message: `查询异常: ${error.message}`,
            data: null,
            error: error
        };
    }
}

/**
 * ===========================================
 * 工具函数和辅助方法
 * ===========================================
 */

/**
 * 格式化设备详情信息
 * @param {string} detailStr - 设备详情JSON字符串
 * @returns {Object|null} 解析后的详情对象
 */
function parseDeviceDetail(detailStr) {
    if (!detailStr) return null;

    try {
        return JSON.parse(detailStr);
    } catch (error) {
        console.warn('[详情解析] 设备详情解析失败:', error);
        return null;
    }
}

/**
 * 格式化时间戳
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间
 */
function formatTimestamp(timeStr) {
    if (!timeStr) return '';

    try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        console.warn('[时间格式化] 时间格式化失败:', error);
        return timeStr;
    }
}

/**
 * 获取告警级别描述
 * @param {number} level - 告警级别
 * @returns {string} 级别描述
 */
function getAlertLevelText(level) {
    const levelMap = {
        1: '一般',
        2: '重要',
        3: '紧急',
        4: '严重'
    };
    return levelMap[level] || '未知';
}

/**
 * 获取设备状态描述
 * @param {number} status - 设备状态
 * @returns {string} 状态描述
 */
function getDeviceStatusText(status) {
    const statusMap = {
        1: '离线',
        2: '在线',
        3: '运行中',
        4: '故障'
    };
    return statusMap[status] || '未知';
}

/**
 * 批量处理API调用
 * @param {Array} apiCalls - API调用数组
 * @returns {Promise<Array>} 所有API调用结果
 */
async function batchApiCall(apiCalls) {
    console.log(`[批量调用] 开始执行${apiCalls.length}个API调用...`);

    try {
        const results = await Promise.allSettled(apiCalls);

        const successCount = results.filter(r => r.status === 'fulfilled').length;
        const failureCount = results.length - successCount;

        console.log(`[批量调用] 完成，成功${successCount}个，失败${failureCount}个`);

        return results.map((result, index) => {
            if (result.status === 'fulfilled') {
                return {
                    index,
                    success: true,
                    data: result.value
                };
            } else {
                return {
                    index,
                    success: false,
                    error: result.reason
                };
            }
        });

    } catch (error) {
        console.error('[批量调用] 批量调用异常:', error);
        throw error;
    }
}

/**
 * 网络配置更新函数
 * @param {Object} newConfig - 新的网络配置
 */
function updateNetworkConfig(newConfig) {
    console.log('[配置更新] 更新网络配置:', newConfig);

    if (newConfig.serverIp) {
        NETWORK_CONFIG.SERVER_IP = newConfig.serverIp;
    }
    if (newConfig.serverPort) {
        NETWORK_CONFIG.SERVER_PORT = newConfig.serverPort;
    }
    if (newConfig.apiBasePath) {
        NETWORK_CONFIG.API_BASE_PATH = newConfig.apiBasePath;
    }

    console.log('[配置更新] 新的API基础URL:', NETWORK_CONFIG.API_BASE_URL);
}

// 全局导出，供其他文件使用
if (typeof window !== 'undefined') {
    // 原有配置和函数
    window.CONFIG = CONFIG;
    window.getAuthHeader = getAuthHeader;
    window.getStrictAuthHeader = getStrictAuthHeader;
    window.getRequestConfig = getRequestConfig;
    window.updateAuthToken = updateAuthToken;
    window.getCurrentToken = getCurrentToken;
    window.refreshToken = refreshToken;
    window.getTokenFromCookies = getTokenFromCookies;

    // 新增网络配置
    window.NETWORK_CONFIG = NETWORK_CONFIG;
    window.API_ENDPOINTS = API_ENDPOINTS;
    window.updateNetworkConfig = updateNetworkConfig;

    // 新增API接口函数
    window.makeHttpRequest = makeHttpRequest;
    window.loginUser = loginUser;
    window.getAlertDeviceList = getAlertDeviceList;
    window.getFaultDeviceList = getFaultDeviceList;
    window.getRealTimeAlarmLogList = getRealTimeAlarmLogList;
    window.processAlarmLogData = processAlarmLogData;
    window.getDeviceRunningStatus = getDeviceRunningStatus;

    // 新增工具函数
    window.parseDeviceDetail = parseDeviceDetail;
    window.formatTimestamp = formatTimestamp;
    window.getAlertLevelText = getAlertLevelText;
    window.getDeviceStatusText = getDeviceStatusText;
    window.batchApiCall = batchApiCall;

    // 通用Cookie和Token工具函数
    window.getCookie = getCookie;
    window.deleteCookie = deleteCookie;
    window.validateToken = validateToken;

    console.log('[配置加载] ✅ 所有API接口功能已加载到全局window对象');
}

// ES6模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        // 原有导出
        CONFIG,
        getAuthHeader,
        getStrictAuthHeader,
        getRequestConfig,
        updateAuthToken,
        getCurrentToken,
        refreshToken,
        getTokenFromCookies,

        // 新增导出
        NETWORK_CONFIG,
        API_ENDPOINTS,
        updateNetworkConfig,
        makeHttpRequest,
        loginUser,
        getAlertDeviceList,
        getFaultDeviceList,
        getDeviceRunningStatus,
        parseDeviceDetail,
        formatTimestamp,
        getAlertLevelText,
        getDeviceStatusText,
        batchApiCall
    };
}