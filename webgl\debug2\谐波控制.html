<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>谐波控制 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 谐波控制专用样式 -->
    <style>
        /* 谐波控制专用样式扩展 */
        .harmonic-param-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
            padding-bottom: 70px;
            overflow: hidden;
            min-height: 0;
        }

        /* 双面板布局样式 */
        .main-layout {
            display: flex;
            flex-direction: row;
            height: calc(100vh - 120px);
            max-height: calc(100vh - 120px);
            gap: 15px;
        }

        .dual-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 浮点数输入框样式 */
        .float-input {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .float-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .float-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        .harmonic-param-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px;
            overflow: hidden;
            min-width: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 0;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px;
            padding: 6px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 表格样式优化 - 每行1个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 8px 6px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 13px;
        }

        .params-table td {
            padding: 6px 4px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 55px;
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 13px;
            color: #7a8ba0;
            font-weight: bold;
            width: 40px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 13px;
            color: #ffffff;
            text-align: center;
            padding: 0 8px;
            line-height: 1.3;
            max-width: 140px;
            width: 140px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 13px;
            color: #00d4ff;
            font-weight: bold;
            width: 100px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 120px;
            text-align: center;
            padding: 0 8px;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>谐波控制</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 左面板：通道Kp系数 -->
                <div class="dual-panel">
                    <div class="harmonic-param-panel">
                        <div class="panel-title">通道Kp系数</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="kp-param-table">
                                    <!-- Kp系数参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右面板：通道Ki系数 -->
                <div class="dual-panel">
                    <div class="harmonic-param-panel">
                        <div class="panel-title">通道Ki系数</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="ki-param-table">
                                    <!-- Ki系数参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 谐波控制参数页面配置
         * 定义谐波控制参数列表和页面设置
         */

        // 通道Kp系数参数组 (8个参数)
        const kpParametersGroup = [
            { mqttId: 'SVG_2320', name: 'H1通道Kp系数' },
            { mqttId: 'SVG_2321', name: 'H2通道Kp系数' },
            { mqttId: 'SVG_2322', name: 'H3通道Kp系数' },
            { mqttId: 'SVG_2323', name: 'H4通道Kp系数' },
            { mqttId: 'SVG_2324', name: 'H5通道Kp系数' },
            { mqttId: 'SVG_2325', name: 'H6通道Kp系数' },
            { mqttId: 'SVG_2326', name: 'H7通道Kp系数' },
            { mqttId: 'SVG_2327', name: 'H8通道Kp系数' }
        ];

        // 通道Ki系数参数组 (8个参数)
        const kiParametersGroup = [
            { mqttId: 'SVG_2328', name: 'H1通道Ki系数' },
            { mqttId: 'SVG_2329', name: 'H2通道Ki系数' },
            { mqttId: 'SVG_2330', name: 'H3通道Ki系数' },
            { mqttId: 'SVG_2331', name: 'H4通道Ki系数' },
            { mqttId: 'SVG_2332', name: 'H5通道Ki系数' },
            { mqttId: 'SVG_2333', name: 'H6通道Ki系数' },
            { mqttId: 'SVG_2334', name: 'H7通道Ki系数' },
            { mqttId: 'SVG_2335', name: 'H8通道Ki系数' }
        ];

        // 合并所有参数
        const allHarmonicParameters = [...kpParametersGroup, ...kiParametersGroup];

        // 谐波控制参数配置对象
        const harmonicParamConfig = {
            parameters: allHarmonicParameters,
            tableConfig: {
                kpTable: {
                    containerId: 'kp-param-table',
                    parameters: kpParametersGroup,
                    title: '通道Kp系数'
                },
                kiTable: {
                    containerId: 'ki-param-table',
                    parameters: kiParametersGroup,
                    title: '通道Ki系数'
                }
            },
            inputType: 'float',
            precision: 4,
            range: { min: -99999.0000, max: 99999.0000 }
        };

        /**
         * 谐波控制参数管理器类
         * 扩展通用参数配置管理器，支持双表格布局
         */
        class HarmonicControlParameterManager extends ParameterConfigManager {
            constructor(config) {
                // 必须先调用super，但传入一个最小配置避免父类的初始化
                super({ parameters: [] });
                
                // 重新设置我们的配置
                this.config = config;
                this.parameters = [];
                this.currentValues = {};
                this.modifiedValues = {};
                this.tableConfig = config.tableConfig;
                this.precision = config.precision || 4;
                this.range = config.range || { min: -99999.0000, max: 99999.0000 };
                
                // 调用我们自定义的渲染方法
                this.renderParameterTable();
            }

            /**
             * 渲染参数表格
             * 重写以支持双表格布局
             */
            renderParameterTable() {
                console.log('开始渲染谐波控制参数表格...');

                // 渲染Kp系数表格
                this.renderSingleTable(this.tableConfig.kpTable);

                // 渲染Ki系数表格
                this.renderSingleTable(this.tableConfig.kiTable);

                console.log('谐波控制参数表格渲染完成');
            }

            /**
             * 渲染单个表格
             * @param {Object} tableConfig - 表格配置
             */
            renderSingleTable(tableConfig) {
                const tableBody = document.getElementById(tableConfig.containerId);
                if (!tableBody) {
                    console.error(`表格容器 ${tableConfig.containerId} 未找到`);
                    return;
                }

                tableBody.innerHTML = '';

                tableConfig.parameters.forEach((param, index) => {
                    const paramId = `param_${param.mqttId}`;
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td class="param-index">${index + 1}</td>
                        <td class="param-name">${param.name}</td>
                        <td class="param-setting">
                            <div class="control-container">
                                <input type="number" 
                                       class="float-input" 
                                       id="input-${paramId}" 
                                       step="0.0001" 
                                       min="${this.range.min}" 
                                       max="${this.range.max}" 
                                       value="0.0000"
                                       onchange="window.parameterManager.updateFloatParam('${paramId}', this)"
                                       onblur="window.parameterManager.validateFloatParam('${paramId}', this)">
                            </div>
                        </td>
                        <td class="param-current" id="current-${paramId}">0.0000</td>
                    `;

                    tableBody.appendChild(row);

                    // 初始化参数对象
                    const parameter = {
                        id: paramId,
                        mqttId: param.mqttId,
                        name: param.name,
                        currentValue: 0.0000,
                        settingValue: 0.0000,
                        isInitialized: false
                    };

                    this.parameters.push(parameter);
                });
            }

            /**
             * 更新浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            updateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseFloat(inputElement.value);
                const min = this.range.min;
                const max = this.range.max;

                if (isNaN(numValue)) {
                    // 无效输入，恢复为当前设定值
                    inputElement.value = param.settingValue.toFixed(this.precision);
                    showStatusMessage('请输入有效的数值', 'warning');
                    return;
                }

                if (numValue < min || numValue > max) {
                    // 超出范围，限制到边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    inputElement.value = boundaryValue.toFixed(this.precision);

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${boundaryValue.toFixed(this.precision)}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = parseFloat(numValue.toFixed(this.precision));
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 验证浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const value = parseFloat(inputElement.value);
                const min = this.range.min;
                const max = this.range.max;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0.0000;
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue.toFixed(this.precision);

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${defaultValue.toFixed(this.precision)}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持浮点数格式化
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) return;

                // 更新参数当前值
                param.currentValue = parseFloat(value) || 0.0000;

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = param.currentValue;
                    param.isInitialized = true;

                    // 更新输入框界面
                    const inputElement = document.getElementById(`input-${param.id}`);
                    if (inputElement) {
                        inputElement.value = parseFloat(param.settingValue).toFixed(this.precision);
                    }

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`首次同步参数 ${param.name}: 当前值=${param.currentValue.toFixed(this.precision)}, 设定值=${param.settingValue.toFixed(this.precision)} (已初始化)`);
                }

                // 更新界面显示（当前值）
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    currentElement.textContent = param.currentValue.toFixed(this.precision);
                    currentElement.style.color = '#00d4ff';
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }
        }

        // 全局谐波控制参数管理器变量
        let harmonicParamManager = null;

        /**
         * 检查参数管理器状态
         * @returns {boolean} 参数管理器是否已正确初始化
         */
        function checkParameterManagerStatus() {
            console.log('检查参数管理器状态...');
            console.log('harmonicParamManager:', harmonicParamManager);
            console.log('window.parameterManager:', window.parameterManager);

            if (!harmonicParamManager) {
                console.error('harmonicParamManager 未初始化');
                return false;
            }

            if (!window.parameterManager) {
                console.error('window.parameterManager 未设置');
                return false;
            }

            console.log('参数管理器状态检查通过');
            return true;
        }

        /**
         * 处理参数设置发送（谐波控制专用）
         */
        async function handleSendParameterSettings() {
            console.log('开始发送谐波控制参数设置...');

            // 检查参数管理器状态
            if (!checkParameterManagerStatus()) {
                showStatusMessage('参数管理器未正确初始化，请刷新页面重试', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = harmonicParamManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = harmonicParamManager.getMQTTParameterArray();

                console.log('准备发送的谐波控制参数:', mqttParams);

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `谐波控制参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('谐波控制参数设置发送成功:', result);

            } catch (error) {
                console.error('发送谐波控制参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 初始化谐波控制参数配置页面
         * @param {Object} config - 谐波控制参数配置对象
         */
        function initHarmonicControlParameterConfigPage(config) {
            console.log('谐波控制参数配置页面初始化...');

            try {
                // 初始化谐波控制参数管理器
                harmonicParamManager = new HarmonicControlParameterManager(config);

                // 设置全局变量以兼容通用脚本
                window.parameterManager = harmonicParamManager;

                // 创建备用的全局发送函数
                window.sendParameterSettings = handleSendParameterSettings;

                console.log('谐波控制参数管理器初始化成功:', harmonicParamManager);
                console.log('全局参数管理器设置成功:', window.parameterManager);

                // 初始化 MQTT 连接
                initMQTTConnection();

                // 定期更新连接状态显示和按钮状态
                setInterval(() => {
                    if (mqttParameterManager) {
                        const status = mqttParameterManager.getConnectionStatus();
                        if (status.isConnected) {
                            updateMQTTStatus('connected', 'MQTT 已连接');
                        } else {
                            updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                        }
                        // 更新发送按钮状态
                        updateSendButtonStatus();
                    }
                }, 1000);

                console.log('谐波控制参数配置页面初始化完成');

            } catch (error) {
                console.error('谐波控制参数配置页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 内容加载完成，开始初始化谐波控制参数配置页面...');

            // 检查必要的依赖
            if (typeof ParameterConfigManager === 'undefined') {
                console.error('ParameterConfigManager 类未定义，请检查通用脚本是否正确加载');
                showStatusMessage('页面初始化失败：缺少必要的依赖脚本', 'error');
                return;
            }

            console.log('依赖检查通过，开始初始化谐波控制参数管理器...');
            console.log('谐波控制参数配置:', harmonicParamConfig);

            // 使用自定义的谐波控制参数配置管理器初始化页面
            try {
                initHarmonicControlParameterConfigPage(harmonicParamConfig);

                // 延迟检查初始化状态
                setTimeout(() => {
                    if (checkParameterManagerStatus()) {
                        console.log('谐波控制参数管理器初始化验证成功');
                        showStatusMessage('页面初始化完成', 'success');
                    } else {
                        console.error('谐波控制参数管理器初始化验证失败');
                        showStatusMessage('参数管理器初始化异常，请刷新页面', 'error');
                    }
                }, 1000);

            } catch (error) {
                console.error('页面初始化过程中发生错误:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>