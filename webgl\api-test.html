<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桂林智源 SVG 系统 - API接口测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .config-section {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .config-section h2 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .config-item {
            display: flex;
            flex-direction: column;
        }

        .config-item label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        .config-item input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }

        .test-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .test-card h3 {
            margin-bottom: 15px;
            color: #ffd700;
            font-size: 1.3em;
        }

        .test-card p {
            margin-bottom: 15px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.success {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .btn.info {
            background: linear-gradient(45deg, #0984e3, #74b9ff);
        }

        .result-area {
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            min-height: 200px;
        }

        .result-area h2 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .result-content {
            background: #1a1a1a;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #00b894; }
        .status-error { background: #e17055; }
        .status-warning { background: #fdcb6e; }
        .status-info { background: #74b9ff; }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: ' 🔄';
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .params-section {
            margin: 15px 0;
        }

        .params-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .params-section input, .params-section select {
            width: 100%;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.9);
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 API接口测试工具</h1>
            <p>桂林智源 SVG 数字化系统 - 接口功能验证平台</p>
        </div>

        <!-- 网络配置区域 -->
        <div class="config-section">
            <h2>🌐 网络配置</h2>
            <div class="config-grid">
                <div class="config-item">
                    <label for="serverIp">服务器IP地址:</label>
                    <input type="text" id="serverIp" value="*************" placeholder="*************">
                </div>
                <div class="config-item">
                    <label for="serverPort">服务器端口:</label>
                    <input type="number" id="serverPort" value="80" placeholder="80">
                </div>
                <div class="config-item">
                    <label for="apiBasePath">API基础路径:</label>
                    <input type="text" id="apiBasePath" value="/prod-api" placeholder="/prod-api">
                </div>
            </div>
            <button class="btn info" onclick="updateConfig()">🔄 更新配置</button>
            <button class="btn" onclick="showCurrentConfig()">📋 查看当前配置</button>
        </div>

        <!-- API测试区域 -->
        <div class="test-section">
            <!-- 登录接口测试 -->
            <div class="test-card">
                <h3>🔐 用户登录接口</h3>
                <p>测试用户认证登录功能，获取JWT Token</p>
                
                <div class="params-section">
                    <label for="loginUsername">用户名:</label>
                    <input type="text" id="loginUsername" value="bydq_admin">
                    
                    <label for="loginPassword">密码:</label>
                    <input type="password" id="loginPassword" value="Aa123456">
                    
                    <label for="loginSourceType">来源类型:</label>
                    <select id="loginSourceType">
                        <option value="1">Web端 (1)</option>
                        <option value="2">移动端 (2)</option>
                    </select>
                </div>
                
                <button class="btn success" onclick="testLogin()">🚀 测试登录</button>
            </div>

            <!-- 报警列表接口测试 -->
            <div class="test-card">
                <h3>⚠️ 报警设备列表</h3>
                <p>获取系统中的报警设备信息列表</p>
                
                <div class="params-section">
                    <label for="alertPageSize">每页数量:</label>
                    <input type="number" id="alertPageSize" value="10" min="1" max="100">
                </div>
                
                <button class="btn info" onclick="testAlertList()">📋 获取报警列表</button>
            </div>

            <!-- 故障列表接口测试 -->
            <div class="test-card">
                <h3>🔥 故障设备列表</h3>
                <p>获取系统中的故障设备信息列表</p>
                
                <div class="params-section">
                    <label for="faultPageSize">每页数量:</label>
                    <input type="number" id="faultPageSize" value="10" min="1" max="100">
                </div>
                
                <button class="btn info" onclick="testFaultList()">📋 获取故障列表</button>
            </div>

            <!-- 设备状态接口测试 -->
            <div class="test-card">
                <h3>📊 设备运行状态</h3>
                <p>获取指定设备的详细运行状态信息</p>
                
                <div class="params-section">
                    <label for="deviceId">设备ID:</label>
                    <input type="number" id="deviceId" value="298" min="1">
                </div>
                
                <button class="btn info" onclick="testDeviceStatus()">📈 获取设备状态</button>
            </div>

            <!-- 批量测试 -->
            <div class="test-card">
                <h3>🔄 批量接口测试</h3>
                <p>同时测试多个接口，验证系统整体功能</p>

                <button class="btn" onclick="testAllApis()">🚀 批量测试所有接口</button>
                <button class="btn success" onclick="clearResults()">🗑️ 清空结果</button>
            </div>

            <!-- Cookie调试 -->
            <div class="test-card">
                <h3>🔍 Cookie认证调试</h3>
                <p>诊断Cookie认证问题，查看详细的调试信息</p>

                <button class="btn info" onclick="debugCookieAuthentication()">🔍 Cookie调试</button>
                <button class="btn" onclick="testCookieFlow()">🔄 测试完整认证流程</button>
                <button class="btn success" onclick="fixCookieIssues()">🔧 尝试修复Cookie问题</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="result-area">
            <h2>📊 测试结果</h2>
            <div class="result-content" id="resultContent">
等待测试结果...

💡 使用说明：
1. 首先配置正确的服务器IP和端口
2. 点击"测试登录"获取认证Token
3. 然后可以测试其他需要认证的接口
4. 查看详细的请求和响应信息
            </div>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <!-- 引入Cookie修复工具 -->
    <script src="cookie-fix.js"></script>
    
    <script>
        // 测试结果显示区域
        const resultContent = document.getElementById('resultContent');
        
        /**
         * 显示测试结果
         * @param {string} title - 测试标题
         * @param {Object} result - 测试结果
         * @param {string} type - 结果类型 (success/error/info/warning)
         */
        function displayResult(title, result, type = 'info') {
            const timestamp = new Date().toLocaleString('zh-CN');
            const statusClass = `status-${type}`;
            
            const resultText = `
[${timestamp}] ${title}
${'='.repeat(50)}
${JSON.stringify(result, null, 2)}
${'='.repeat(50)}

`;
            
            resultContent.textContent = resultText + resultContent.textContent;
            resultContent.scrollTop = 0;
        }
        
        /**
         * 更新网络配置
         */
        function updateConfig() {
            const serverIp = document.getElementById('serverIp').value;
            const serverPort = parseInt(document.getElementById('serverPort').value);
            const apiBasePath = document.getElementById('apiBasePath').value;
            
            updateNetworkConfig({
                serverIp,
                serverPort,
                apiBasePath
            });
            
            displayResult('🔄 配置更新', {
                serverIp,
                serverPort,
                apiBasePath,
                newApiBaseUrl: NETWORK_CONFIG.API_BASE_URL
            }, 'success');
        }
        
        /**
         * 显示当前配置
         */
        function showCurrentConfig() {
            displayResult('📋 当前配置', {
                networkConfig: NETWORK_CONFIG,
                apiEndpoints: API_ENDPOINTS,
                currentToken: getCurrentToken() ? '已设置' : '未设置'
            }, 'info');
        }
        
        /**
         * 测试登录接口
         */
        async function testLogin() {
            const btn = event.target;
            btn.classList.add('loading');
            
            try {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;
                const sourceType = parseInt(document.getElementById('loginSourceType').value);
                
                displayResult('🔐 开始登录测试', { username, sourceType }, 'info');
                
                const result = await loginUser(username, password, '', sourceType);
                
                displayResult('🔐 登录测试结果', result, result.success ? 'success' : 'error');
                
            } catch (error) {
                displayResult('🔐 登录测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        /**
         * 测试报警列表接口
         */
        async function testAlertList() {
            const btn = event.target;
            btn.classList.add('loading');
            
            try {
                const pageSize = parseInt(document.getElementById('alertPageSize').value);
                
                displayResult('⚠️ 开始报警列表测试', { pageSize }, 'info');
                
                const result = await getAlertDeviceList({ pageSize });
                
                displayResult('⚠️ 报警列表测试结果', result, result.success ? 'success' : 'error');
                
            } catch (error) {
                displayResult('⚠️ 报警列表测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        /**
         * 测试故障列表接口
         */
        async function testFaultList() {
            const btn = event.target;
            btn.classList.add('loading');
            
            try {
                const pageSize = parseInt(document.getElementById('faultPageSize').value);
                
                displayResult('🔥 开始故障列表测试', { pageSize }, 'info');
                
                const result = await getFaultDeviceList({ pageSize });
                
                displayResult('🔥 故障列表测试结果', result, result.success ? 'success' : 'error');
                
            } catch (error) {
                displayResult('🔥 故障列表测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        /**
         * 测试设备状态接口
         */
        async function testDeviceStatus() {
            const btn = event.target;
            btn.classList.add('loading');
            
            try {
                const deviceId = parseInt(document.getElementById('deviceId').value);
                
                displayResult('📊 开始设备状态测试', { deviceId }, 'info');
                
                const result = await getDeviceRunningStatus(deviceId);
                
                displayResult('📊 设备状态测试结果', result, result.success ? 'success' : 'error');
                
            } catch (error) {
                displayResult('📊 设备状态测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        /**
         * 批量测试所有接口
         */
        async function testAllApis() {
            const btn = event.target;
            btn.classList.add('loading');
            
            try {
                displayResult('🚀 开始批量接口测试', { timestamp: new Date().toISOString() }, 'info');
                
                // 先顺序登录，确保Cookie中写入Admin-Token
                const loginRes = await loginUser('bydq_admin', 'Aa123456');
                displayResult('🔑 登录结果', loginRes, loginRes.success ? 'success' : 'error');
                if (!loginRes.success) {
                    throw new Error('登录失败，无法继续执行后续接口测试');
                }

                // 再并发调用其他需要认证的接口
                const apiCalls = [
                    getAlertDeviceList({ pageSize: 5 }),
                    getFaultDeviceList({ pageSize: 5 }),
                    getDeviceRunningStatus(298)
                ];

                const results = await batchApiCall(apiCalls);

                displayResult('🚀 批量测试完成', {
                    totalCalls: results.length,
                    successCount: results.filter(r => r.success).length,
                    failureCount: results.filter(r => !r.success).length,
                    results: results
                }, 'success');
                
            } catch (error) {
                displayResult('🚀 批量测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        /**
         * 清空测试结果
         */
        function clearResults() {
            resultContent.textContent = '测试结果已清空，等待新的测试...\n\n';
        }

        /**
         * Cookie认证调试
         */
        function debugCookieAuthentication() {
            const btn = event.target;
            btn.classList.add('loading');

            try {
                displayResult('🔍 开始Cookie认证调试', { timestamp: new Date().toISOString() }, 'info');

                const debugInfo = debugCookieAuth();

                displayResult('🔍 Cookie认证调试结果', debugInfo, 'info');

                // 额外显示关键信息
                const summary = {
                    hasAdminToken: debugInfo.cookies.hasAdminToken,
                    tokenFromCookie: debugInfo.token.fromCookie.exists,
                    usingFallback: debugInfo.token.usingFallback,
                    environment: `${debugInfo.environment.protocol}//${debugInfo.environment.hostname}`,
                    recommendations: debugInfo.recommendations
                };

                displayResult('🔍 调试摘要', summary, debugInfo.recommendations.some(r => r.includes('❌')) ? 'error' : 'success');

            } catch (error) {
                displayResult('🔍 Cookie调试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }

        /**
         * 测试完整认证流程
         */
        async function testCookieFlow() {
            const btn = event.target;
            btn.classList.add('loading');

            try {
                displayResult('🔄 开始完整认证流程测试', { timestamp: new Date().toISOString() }, 'info');

                // 1. 清空现有Cookie
                if (typeof document !== 'undefined') {
                    document.cookie = 'Admin-Token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
                    displayResult('🗑️ 已清空现有Cookie', {}, 'info');
                }

                // 2. 执行登录
                const loginResult = await loginUser('bydq_admin', 'Aa123456');
                displayResult('🔐 登录结果', loginResult, loginResult.success ? 'success' : 'error');

                if (!loginResult.success) {
                    throw new Error('登录失败，无法继续测试');
                }

                // 3. 等待一下让Cookie写入生效
                await new Promise(resolve => setTimeout(resolve, 100));

                // 4. 检查Cookie状态
                const cookieDebug = debugCookieAuth();
                displayResult('🔍 登录后Cookie状态', cookieDebug, 'info');

                // 5. 测试需要认证的API
                const alertResult = await getAlertDeviceList({ pageSize: 5 });
                displayResult('⚠️ 认证API测试结果', alertResult, alertResult.success ? 'success' : 'error');

                // 6. 最终总结
                const finalSummary = {
                    loginSuccess: loginResult.success,
                    cookieWritten: cookieDebug.cookies.hasAdminToken,
                    apiCallSuccess: alertResult.success,
                    overallSuccess: loginResult.success && cookieDebug.cookies.hasAdminToken && alertResult.success
                };

                displayResult('🎯 完整流程测试总结', finalSummary, finalSummary.overallSuccess ? 'success' : 'error');

            } catch (error) {
                displayResult('🔄 认证流程测试异常', { error: error.message }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }

        /**
         * 尝试修复Cookie问题（使用专业修复工具）
         */
        async function fixCookieIssues() {
            const btn = event.target;
            btn.classList.add('loading');

            try {
                displayResult('🔧 开始使用专业工具修复Cookie问题', { timestamp: new Date().toISOString() }, 'info');

                // 1. 使用CookieFixer进行初步诊断
                const initialDiagnosis = cookieFixer.diagnoseCookieIssues();
                displayResult('🔍 专业诊断结果', initialDiagnosis, 'info');

                // 2. 如果没有Token，先登录获取Token
                let token = null;
                if (!initialDiagnosis.hasAdminToken) {
                    displayResult('🔐 检测到无Token，开始登录获取Token...', {}, 'info');
                    const loginResult = await loginUser('bydq_admin', 'Aa123456');

                    if (!loginResult.success) {
                        throw new Error('登录失败，无法获取Token进行修复');
                    }

                    token = loginResult.token;
                    displayResult('🔐 登录成功，获得Token', { tokenLength: token.length }, 'success');
                } else {
                    // 从现有Cookie中获取Token
                    token = initialDiagnosis.cookieInfo.parsed[cookieFixer.tokenName];
                    try {
                        token = decodeURIComponent(token);
                    } catch (e) {
                        // 如果解码失败，使用原值
                    }
                    displayResult('🔍 从现有Cookie获取Token', { tokenLength: token.length }, 'info');
                }

                // 3. 使用专业工具执行修复
                const fixResult = await cookieFixer.fixCookieIssues(token);
                displayResult('🔧 专业修复工具执行结果', fixResult, fixResult.success ? 'success' : 'warning');

                // 4. 验证修复效果 - 测试API调用
                displayResult('🧪 开始验证修复效果...', {}, 'info');
                const testResult = await getAlertDeviceList({ pageSize: 3 });
                displayResult('🧪 API调用验证结果', testResult, testResult.success ? 'success' : 'error');

                // 5. 生成最终报告
                const finalReport = {
                    修复工具版本: 'CookieFixer v1.0',
                    修复耗时: `${fixResult.duration}ms`,
                    初始问题数: fixResult.initialDiagnosis.issues.length,
                    修复后问题数: fixResult.finalDiagnosis.issues.length,
                    问题修复数: fixResult.summary.issuesFixed,
                    Token写入成功: fixResult.summary.tokenWritten,
                    Token验证成功: fixResult.summary.tokenVerified,
                    API调用成功: testResult.success,
                    整体修复成功: fixResult.success && testResult.success,
                    使用的写入策略: fixResult.writeResult.bestStrategy?.name || '无成功策略',
                    最终建议: fixResult.finalDiagnosis.recommendations
                };

                displayResult('🎯 最终修复报告', finalReport, finalReport.整体修复成功 ? 'success' : 'error');

            } catch (error) {
                displayResult('🔧 修复过程发生异常', {
                    error: error.message,
                    stack: error.stack,
                    建议: '请检查网络连接和服务器状态，或联系技术支持'
                }, 'error');
            } finally {
                btn.classList.remove('loading');
            }
        }
        
        // 页面加载完成后显示初始信息
        document.addEventListener('DOMContentLoaded', function() {
            displayResult('🎉 API测试工具已加载', {
                timestamp: new Date().toISOString(),
                availableApis: Object.keys(API_ENDPOINTS),
                currentConfig: NETWORK_CONFIG.API_BASE_URL
            }, 'success');
        });
    </script>
</body>
</html>
