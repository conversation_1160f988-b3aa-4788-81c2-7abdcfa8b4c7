<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT 电气系统数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .test-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 6px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .log-panel {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .data-item {
            background: white;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
        .data-item.active {
            border-color: #28a745;
            background: #f8fff8;
        }
        .data-item.inactive {
            border-color: #dc3545;
            background: #fff8f8;
        }
        .connected { color: #28a745; }
        .disconnected { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MQTT 电气系统数据测试</h1>
            <p>白云电气设备数字孪生系统 - 实时数据监控测试</p>
        </div>

        <div class="status-panel">
            <div class="status-card">
                <h3>连接状态</h3>
                <div class="status-value" id="connection-status">未连接</div>
            </div>
            <div class="status-card">
                <h3>数据接收</h3>
                <div class="status-value" id="data-received">0</div>
            </div>
            <div class="status-card">
                <h3>数据质量</h3>
                <div class="status-value" id="data-quality">0%</div>
            </div>
            <div class="status-card">
                <h3>最后更新</h3>
                <div class="status-value" id="last-update">--</div>
            </div>
        </div>

        <div class="test-controls">
            <h3>测试控制</h3>
            <button class="btn btn-primary" onclick="connectMQTT()">连接 MQTT</button>
            <button class="btn btn-warning" onclick="disconnectMQTT()">断开连接</button>
            <button class="btn btn-success" onclick="sendTestData()">发送测试数据</button>
            <button class="btn btn-warning" onclick="runAllTests()">运行数据处理测试</button>
            <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
            <button class="btn btn-primary" onclick="resetStatistics()">重置统计</button>
        </div>

        <div class="data-grid" id="data-grid">
            <!-- 电气系统参数将在这里显示 -->
        </div>

        <h3>系统日志</h3>
        <div class="log-panel" id="log-panel">
            <div>系统初始化中...</div>
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <script src="electricalDataProcessor.js"></script>
    <script src="test-data-processing.js"></script>

    <script>
        // 全局变量
        let mqttClient = null;
        let dataProcessor = null;
        let isConnected = false;
        let dataReceived = 0;
        let testInterval = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTest();
        });

        /**
         * 初始化测试环境
         */
        function initializeTest() {
            log('初始化 MQTT 电气系统数据测试环境...');
            
            // 初始化数据处理器
            dataProcessor = new ElectricalDataProcessor();
            log('数据处理器初始化完成');
            
            // 创建数据显示网格
            createDataGrid();
            
            // 更新状态显示
            updateStatusDisplay();
            
            log('测试环境初始化完成');
        }

        /**
         * 连接 MQTT
         */
        function connectMQTT() {
            if (isConnected) {
                log('MQTT 已连接');
                return;
            }

            log('正在连接 MQTT 服务器...');
            
            const options = {
                username: 'FastBee',
                password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.1O79OeRWpHjRa5nps9L9ETtplMTLmUiiZpmLi-waRkGnM4M72fgWdj4QGxqx_-ccCedyixw5jpfeWtniY5RHXQ',
                cleanSession: true,
                keepAlive: 30,
                clientId: 'web-' + Math.random().toString(16).substr(2),
                connectTimeout: 60000,
            };

            // 使用实际的 MQTT 地址
            const url = 'ws://192.168.0.205:8083/mqtt';
            
            try {
                mqttClient = mqtt.connect(url, options);
                
                mqttClient.on('connect', () => {
                    isConnected = true;
                    log('MQTT 连接成功');
                    updateStatusDisplay();
                    
                    // 订阅实际主题
                    const topic = '/189/D19QBHKRZ791U/ws/service';
                    mqttClient.subscribe(topic, { qos: 1 }, (err) => {
                        if (err) {
                            log('订阅失败: ' + err.message, 'error');
                        } else {
                            log('成功订阅主题: ' + topic);
                        }
                    });
                });

                mqttClient.on('message', (topic, message) => {
                    handleMQTTMessage(topic, message);
                });

                mqttClient.on('error', (error) => {
                    log('MQTT 连接错误: ' + error.message, 'error');
                    isConnected = false;
                    updateStatusDisplay();
                });

                mqttClient.on('close', () => {
                    log('MQTT 连接已断开');
                    isConnected = false;
                    updateStatusDisplay();
                });

            } catch (error) {
                log('MQTT 连接失败: ' + error.message, 'error');
            }
        }

        /**
         * 断开 MQTT 连接
         */
        function disconnectMQTT() {
            if (mqttClient) {
                mqttClient.end();
                mqttClient = null;
            }
            isConnected = false;
            updateStatusDisplay();
            log('MQTT 连接已断开');
        }

        /**
         * 处理 MQTT 消息
         */
        function handleMQTTMessage(topic, message) {
            try {
                const data = JSON.parse(message.toString());
                log('收到 MQTT 消息: ' + JSON.stringify(data));
                
                // 使用数据处理器处理数据
                const result = dataProcessor.processData(data);
                
                if (result.success) {
                    dataReceived++;
                    updateDataDisplay(result.data, result.metadata);
                    updateStatusDisplay();
                    log('数据处理成功，质量: ' + result.metadata.dataQuality.score.toFixed(1) + '%');
                } else {
                    log('数据处理失败: ' + result.error, 'error');
                }
                
            } catch (error) {
                log('消息处理错误: ' + error.message, 'error');
            }
        }

        /**
         * 发送测试数据
         */
        function sendTestData() {
            const testData = {
                message: [
                    {"id":"HMI_30039_5","name":"运行","ts":new Date().toISOString(),"value": Math.random() > 0.5 ? "1" : "0"},
                    {"id":"HMI_30039_6","name":"故障","ts":new Date().toISOString(),"value": Math.random() > 0.8 ? "1" : "0"},
                    {"id":"HMI_30039_4","name":"就绪","ts":new Date().toISOString(),"value": Math.random() > 0.3 ? "1" : "0"},
                    {"id":"HMI_30039_9","name":"备用","ts":new Date().toISOString(),"value": Math.random() > 0.7 ? "1" : "0"},
                    {"id":"HMI_30039_7","name":"高压","ts":new Date().toISOString(),"value": Math.random() > 0.6 ? "1" : "0"},
                    {"id":"HMI_32030","name":"母线电压Uab","ts":new Date().toISOString(),"value": (10 + Math.random() * 2).toFixed(2)},
                    {"id":"HMI_32032","name":"母线电压Ubc","ts":new Date().toISOString(),"value": (10 + Math.random() * 2).toFixed(2)},
                    {"id":"HMI_32050","name":"功率因数","ts":new Date().toISOString(),"value": (0.8 + Math.random() * 0.2).toFixed(3)},
                    {"id":"HMI_32048","name":"负载无功功率","ts":new Date().toISOString(),"value": (-5 + Math.random() * 10).toFixed(2)}
                ],
                sources: JSON.stringify([
                    {"id":"HMI_30039_5","value": Math.random() > 0.5 ? 1 : 0},
                    {"id":"HMI_30039_6","value": Math.random() > 0.8 ? 1 : 0}
                ])
            };

            log('发送测试数据: ' + JSON.stringify(testData));

            // 模拟接收数据
            handleMQTTMessage('/test/topic', JSON.stringify(testData));
        }

        /**
         * 创建数据显示网格
         */
        function createDataGrid() {
            const grid = document.getElementById('data-grid');
            const properties = dataProcessor.getSupportedProperties();
            
            properties.forEach(prop => {
                const item = document.createElement('div');
                item.className = 'data-item';
                item.id = 'data-' + prop.id;
                item.innerHTML = `
                    <h4>${prop.name}</h4>
                    <div>ID: ${prop.id}</div>
                    <div>类型: ${prop.type}</div>
                    <div>单位: ${prop.unit || '无'}</div>
                    <div class="status-value">值: <span id="value-${prop.id}">--</span></div>
                `;
                grid.appendChild(item);
            });
        }

        /**
         * 更新数据显示
         */
        function updateDataDisplay(data, metadata) {
            if (data.properties) {
                Object.keys(data.properties).forEach(propId => {
                    const prop = data.properties[propId];
                    const valueElement = document.getElementById('value-' + propId);
                    const itemElement = document.getElementById('data-' + propId);
                    
                    if (valueElement) {
                        valueElement.textContent = prop.value + ' ' + prop.unit;
                    }
                    
                    if (itemElement) {
                        itemElement.className = 'data-item ' + (prop.value > 0 ? 'active' : 'inactive');
                    }
                });
            }
        }

        /**
         * 更新状态显示
         */
        function updateStatusDisplay() {
            document.getElementById('connection-status').textContent = isConnected ? '已连接' : '未连接';
            document.getElementById('connection-status').className = 'status-value ' + (isConnected ? 'connected' : 'disconnected');
            
            document.getElementById('data-received').textContent = dataReceived;
            
            if (dataProcessor) {
                const stats = dataProcessor.getStatistics();
                document.getElementById('data-quality').textContent = stats.successRate.toFixed(1) + '%';
                document.getElementById('last-update').textContent = stats.lastProcessTime ? 
                    stats.lastProcessTime.toLocaleTimeString() : '--';
            }
        }

        /**
         * 清空日志
         */
        function clearLog() {
            document.getElementById('log-panel').innerHTML = '';
        }

        /**
         * 重置统计
         */
        function resetStatistics() {
            if (dataProcessor) {
                dataProcessor.resetStatistics();
            }
            dataReceived = 0;
            updateStatusDisplay();
            log('统计信息已重置');
        }

        /**
         * 记录日志
         */
        function log(message, type = 'info') {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            let color = '#00ff00';
            if (type === 'error') color = '#ff4444';
            if (type === 'warning') color = '#ffaa00';
            
            logEntry.style.color = color;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (testInterval) {
                clearInterval(testInterval);
            }
            disconnectMQTT();
        });
    </script>
</body>
</html>
