<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>旁路控制 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>旁路控制</h1>
        </div>

        <!-- 控制头部 -->
        <div class="control-header">
            <div class="unit-count">
                <span>单元数量：</span>
                <select class="unit-count-select" id="unit-count" onchange="updateUnitCount()">
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                </select>
            </div>
            <div>
                <!-- 旁路控制文本和开始按钮已移除 -->
            </div>
        </div>

        <!-- 三相面板 -->
        <div class="phase-panels">
            <!-- A相 -->
            <div class="phase-panel">
                <div class="phase-title">A相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-a-table">
                        <!-- A相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- B相 -->
            <div class="phase-panel">
                <div class="phase-title">B相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-b-table">
                        <!-- B相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- C相 -->
            <div class="phase-panel">
                <div class="phase-title">C相</div>
                <table class="phase-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>设定值</th>
                            <th>当前值</th>
                        </tr>
                    </thead>
                    <tbody id="phase-c-table">
                        <!-- C相数据将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <button class="send-button" id="download-btn" onclick="downloadBypassSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <script>
        // 旁路控制页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('旁路控制页面初始化...');

            // 初始化旁路控制管理器
            const manager = initBypassControlManager();

            // 初始化三相表格
            initPhaseTables();

            // 初始化MQTT连接
            manager.initMQTTConnection();

            // 定期更新时间戳
            setInterval(() => {
                if (manager.isConnected) {
                    updateDataTimestamp(new Date());
                }
            }, 1000);
        });
    </script>
</body>
</html>